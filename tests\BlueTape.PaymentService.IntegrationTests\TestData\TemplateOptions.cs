﻿using BlueTape.PaymentService.Domain.Models;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Models.Raw;

namespace BlueTape.PaymentService.IntegrationTests.TestData;

public class TemplateOptions
{
    public static int GetTemplateSteps(string templateCode) => RawTemplateOptions.Templates
        .Where(x => x.FlowTemplateCode == templateCode)
        .Select(x => x.Steps.Count())
        .DefaultIfEmpty(0)
        .First();

    public static readonly RawFlowTemplate InvoicePaymentTemplateOptions = new()
    {
        FlowTemplateCode = "CREATE.PAYNOW.INVOICE_PAYMENT",
        TemplateName = "PayNow ACH Flow",
        Product = "PayNow",
        PaymentSubscription = "SUBSCRIPTION1",
        Version = "1.0",
        Steps =
        [
            new RawFlowTemplateStep()
            {
                Sequence = 1,
                Name = "PullFromCustomer",
                TransactionType = "AchPull",
                Condition = null,
                ConditionType = "None",
                TransactionAmount =
                [
                    "PaymentRequestAmount", "+PurchaserFee"
                ],
                DelayDaysType = "None",
                OriginatorAccount = "COLLECTION",
                ReceiverAccount = "BORROWER"
            },
            new RawFlowTemplateStep()
            {
                Sequence = 2,
                Name = "CollectToFunding",
                TransactionType = "AchInternal",
                Condition = "PullFromCustomer",
                ConditionType = "Wait",
                TransactionAmount = new[]
                {
                    "PaymentRequestAmount", "-MerchantFee"
                },
                DelayDaysType = null,
                OriginatorAccount = "FUNDING",
                ReceiverAccount = "COLLECTION"
            },
            new RawFlowTemplateStep()
            {
                Sequence = 3,
                Name = "CollectToRevenue",
                TransactionType = "AchInternal",
                Condition = "CollectToFunding",
                ConditionType = "Wait",
                TransactionAmount = new[]
                {
                    "+PurchaserFee", "+MerchantFee"
                },
                DelayDaysType = "None",
                OriginatorAccount = "REVENUE",
                ReceiverAccount = "COLLECTION"
            },
            new RawFlowTemplateStep()
            {
                Sequence = 4,
                Name = "PushToMerchant",
                TransactionType = "AchPush",
                Condition = "CollectToRevenue",
                ConditionType = "Wait",
                TransactionAmount =
                [
                    "PaymentRequestAmount", "-MerchantFee"
                ],
                DelayDaysType = "MerchantAchDelay",
                OriginatorAccount = "MERCHANT",
                ReceiverAccount = "FUNDING"
            }
        ],
        RetryPolicy = new RetryPolicySettings()
        {
            NotEnoughBalanceMaxPeriodInDays = 5,
            TooManyRequestsMaxAttemptsCount = 5
        },
    };

    public static readonly RawFlowTemplate InvoicePaymentCardTemplateOptions = new()
    {
        FlowTemplateCode = "CREATE.PAYNOW.INVOICE_PAYMENT.CARD",
        TemplateName = "PayNow ACH Flow",
        Product = "PayNow",
        PaymentSubscription = "SUBSCRIPTION1",
        Version = "1.0",
        Steps =
        [
            new RawFlowTemplateStep()
            {
                Sequence = 1,
                Name = "MoveCardPaymentToFunding",
                TransactionType = "AchPull",
                Condition = "WaitForEligibleBalanceFIFO",
                ConditionType = "wait",
                TransactionAmount =
                [
                    "paymentRequestAmount",
                    "-merchantFee"
                ],
                DelayDaysType = "None",
                OriginatorAccount = "FUNDING",
                ReceiverAccount = "CARD-COLLECTION"
            },
            new RawFlowTemplateStep()
            {
                Sequence = 2,
                Name = "PushToMerchant",
                TransactionType = "AchPush",
                Condition = "CollectToRevenue",
                ConditionType = "Wait",
                TransactionAmount =
                [
                    "PaymentRequestAmount", "-MerchantFee"
                ],
                DelayDaysType = "MerchantAchDelay",
                OriginatorAccount = "MERCHANT",
                ReceiverAccount = "FUNDING"
            }
        ],
        RetryPolicy = new RetryPolicySettings()
        {
            NotEnoughBalanceMaxPeriodInDays = 5,
            TooManyRequestsMaxAttemptsCount = 5
        },
    };

    public static readonly RawFlowTemplate InvoicePaymentRetryTemplateOptions = new()
    {
        FlowTemplateCode = "CREATE.PAYNOW.INVOICE_PAYMENT",
        TemplateName = "PayNow ACH Flow",
        Product = "PayNow",
        PaymentSubscription = "SUBSCRIPTION1",
        Version = "1.0",
        Steps =
        [
            new RawFlowTemplateStep()
            {
                Sequence = 1,
                Name = "PullFromCustomer",
                TransactionType = "AchPull",
                Condition = null,
                ConditionType = "None",
                TransactionAmount =
                [
                    "PaymentRequestAmount", "+PurchaserFee"
                ],
                DelayDaysType = "None",
                OriginatorAccount = "COLLECTION",
                ReceiverAccount = "BORROWER"
            },
            new RawFlowTemplateStep()
            {
                Sequence = 2,
                Name = "CollectToFunding",
                TransactionType = "AchInternal",
                Condition = "PullFromCustomer",
                ConditionType = "Wait",
                TransactionAmount = new[]
                {
                    "PaymentRequestAmount", "-MerchantFee"
                },
                DelayDaysType = null,
                OriginatorAccount = "FUNDING",
                ReceiverAccount = "COLLECTION"
            },
            new RawFlowTemplateStep()
            {
                Sequence = 3,
                Name = "CollectToRevenue",
                TransactionType = "AchInternal",
                Condition = "CollectToFunding",
                ConditionType = "Wait",
                TransactionAmount = new[]
                {
                    "+PurchaserFee", "+MerchantFee"
                },
                DelayDaysType = "None",
                OriginatorAccount = "REVENUE",
                ReceiverAccount = "COLLECTION"
            },
            new RawFlowTemplateStep()
            {
                Sequence = 4,
                Name = "PushToMerchant",
                TransactionType = "AchPush",
                Condition = "CollectToRevenue",
                ConditionType = "Wait",
                TransactionAmount =
                [
                    "PaymentRequestAmount", "-MerchantFee"
                ],
                DelayDaysType = "MerchantAchDelay",
                OriginatorAccount = "MERCHANT",
                ReceiverAccount = "FUNDING"
            }
        ],
        RetryPolicy = new RetryPolicySettings()
        {
            NotEnoughBalanceMaxPeriodInDays = 5,
            TooManyRequestsMaxAttemptsCount = 5
        },
    };

    public static readonly RawFlowTemplate InvoicePaymentV2TemplateOptions = new()
    {
        FlowTemplateCode = "CREATE.PAYNOW.INVOICE_PAYMENT.V2",
        TemplateName = "PayNow ACH Flow v2",
        Product = "PayNow",
        PaymentSubscription = "SUBSCRIPTION2",
        Version = "1.0",
        Steps =
        [
            new RawFlowTemplateStep()
            {
                Sequence = 1,
                Name = "PullFromCustomer",
                TransactionType = "AchPull",
                Condition = null,
                ConditionType = "None",
                TransactionAmount =
                [
                    "PaymentRequestAmount", "+PurchaserFee"
                ],
                DelayDaysType = "None",
                OriginatorAccount = "DynamicSpvCollection",
                ReceiverAccount = "BORROWER"
            },
        ],
        RetryPolicy = new RetryPolicySettings()
        {
            NotEnoughBalanceMaxPeriodInDays = 5,
            TooManyRequestsMaxAttemptsCount = 5
        },
    };

    public static readonly RawFlowTemplate DrawRepaymentFlowTemplateOptions = new()
    {
        FlowTemplateCode = "CREATE.DRAW.REPAYMENT",
        TemplateName = "Draw Repayment Flow",
        Product = "TradeCredit",
        PaymentSubscription = "SUBSCRIPTION2",
        Version = "1.0",
        Steps =
        [
            new RawFlowTemplateStep()
            {
                Sequence = 1,
                Name = "CollectFromBorrower",
                TransactionType = "AchPull",
                Condition = null,
                ConditionType = "None",
                TransactionAmount = ["RepaymentAmount"],
                DelayDaysType = "AchHold",
                OriginatorAccount = "DynamicSpvCollection",
                ReceiverAccount = "BORROWER"
            }
        ],
        RetryPolicy = new RetryPolicySettings()
        {
            NotEnoughBalanceMaxPeriodInDays = 5,
            TooManyRequestsMaxAttemptsCount = 5
        },
    };

    public static readonly RawFlowTemplate DrawRepaymentCardFlowTemplateOptions = new()
    {
        FlowTemplateCode = "CREATE.DRAW.REPAYMENT.CARD",
        TemplateName = "Draw Repayment Flow",
        Product = "TradeCredit",
        PaymentSubscription = "SUBSCRIPTION2",
        Version = "1.0",
        Steps =
        [
            new RawFlowTemplateStep()
            {
                Sequence = 1,
                Name = "MoveCardPaymentToCollection",
                TransactionType = "AchPull",
                Condition = null,
                ConditionType = "None",
                TransactionAmount = ["RepaymentAmount"],
                DelayDaysType = "AchHold",
                OriginatorAccount = "DynamicSpvCollection",
                ReceiverAccount = "BORROWER"
            }
        ],
        RetryPolicy = new RetryPolicySettings()
        {
            NotEnoughBalanceMaxPeriodInDays = 5,
            TooManyRequestsMaxAttemptsCount = 5
        },
    };

    public static readonly RawFlowTemplate DrawRepaymentManualFlowTemplateOptions = new()
    {
        FlowTemplateCode = "CREATE.DRAW.REPAYMENT.MANUAL",
        TemplateName = "Lockbox/DACA Draw Repayment Flow",
        Product = "TradeCredit",
        PaymentSubscription = "SUBSCRIPTION2",
        Version = "1.0",
        Steps =
        [
            new RawFlowTemplateStep()
            {
                Sequence = 1,
                Name = "CollectFromLockboxOrDaca",
                TransactionType = "AchInternal",
                Condition = null,
                ConditionType = "None",
                TransactionAmount = ["RepaymentAmount"],
                DelayDaysType = "None",
                OriginatorAccount = "DynamicSpvCollection",
                ReceiverAccount = "MANUALPAYMENT"
            }
        ],
        RetryPolicy = new RetryPolicySettings()
        {
            NotEnoughBalanceMaxPeriodInDays = 5,
            TooManyRequestsMaxAttemptsCount = 5
        },
    };

    public static readonly RawFlowTemplate ManualPaymentPullFlowTemplateOptions = new()
    {
        FlowTemplateCode = "CREATE.MANUAL.PAYMENT.PULL",
        TemplateName = "Manual ach pull flow",
        Product = "PayNow",
        PaymentSubscription = "SUBSCRIPTION1",
        Version = "1.0",
        Steps =
        [
            new RawFlowTemplateStep()
            {
                Sequence = 1,
                Name = "PullFromCustomer",
                TransactionType = "AchPull",
                Condition = null,
                ConditionType = "None",
                TransactionAmount = ["ManualPaymentAmount"],
                DelayDaysType = "None",
                OriginatorAccount = "MANUALPAYMENT",
                ReceiverAccount = "BORROWER"
            }
        ],
        RetryPolicy = new RetryPolicySettings()
        {
            NotEnoughBalanceMaxPeriodInDays = 5,
            TooManyRequestsMaxAttemptsCount = 5
        },
    };

    public static readonly RawFlowTemplate SubscriptionFeePaymentFlowTemplateOptions = new()
    {
        FlowTemplateCode = "CREATE.SUBSCRIPTIONFEE.PAYMENT",
        TemplateName = "Pull Supplier Subscription Fee Flow",
        Product = "",
        PaymentSubscription = "SUBSCRIPTION1",
        Version = "1.0",
        Steps =
        [
            new RawFlowTemplateStep()
            {
                Sequence = 1,
                Name = "CollectFromMerchant",
                TransactionType = "AchPull",
                Condition = null,
                ConditionType = "None",
                TransactionAmount = ["RequestedAmount"],
                DelayDaysType = "AchHold",
                OriginatorAccount = "REVENUE",
                ReceiverAccount = "MERCHANT"
            }
        ]
    };


    public static readonly RawFlowTemplate FinalPaymentV2FlowTemplateOptions = new()
    {
        FlowTemplateCode = "CREATE.DRAW.FINALPAYMENT",
        TemplateName = "Draw Final Payment Flow",
        Product = "TradeCredit",
        PaymentSubscription = "SUBSCRIPTION2",
        Version = "1.0",
        Steps =
        [
            new RawFlowTemplateStep()
            {
                Sequence = 1,
                Name = "PushFinalPaymentToMerchant",
                TransactionType = "AchPush",
                Condition = "None",
                ConditionType = "None",
                TransactionAmount =
                [
                    "FinalAmount",
                    "-MerchantFee",
                    "-customerFees" // TBC
                ],
                DelayDaysType = "AchHold",
                OriginatorAccount = "MERCHANT",
                ReceiverAccount = "DynamicSpvFunding"
            }
        ],
        RetryPolicy = new RetryPolicySettings()
        {
            NotEnoughBalanceMaxPeriodInDays = 5,
            TooManyRequestsMaxAttemptsCount = 5
        }
    };

    public static readonly RawFlowTemplate FinalPaymentFlowTemplateOptions = new()
    {
        FlowTemplateCode = "CREATE.DRAW.FINALPAYMENT.V2",
        TemplateName = "Draw Final Payment Flow",
        Product = "TradeCredit",
        PaymentSubscription = "SUBSCRIPTION2",
        Version = "1.0",
        Steps =
        [
            new RawFlowTemplateStep()
            {
                Sequence = 1,
                Name = "CollectionToFunding",
                TransactionType = "AchInternal",
                Condition = null,
                ConditionType = "None",
                TransactionAmount =
                [
                    "FinalAmount",
                    "-MerchantFee",
                    "-customerFees" // TBC
                ],
                DelayDaysType = "AchHold",
                OriginatorAccount = "DynamicSpvFunding",
                ReceiverAccount = "DynamicSpvCollection"
            },
            new RawFlowTemplateStep()
            {
                Sequence = 2,
                Name = "PushFinalPaymentToMerchant",
                TransactionType = "AchPush",
                Condition = "CollectionToFunding",
                ConditionType = "Wait",
                TransactionAmount =
                [
                    "FinalAmount",
                    "-MerchantFee",
                    "-customerFees" // TBC
                ],
                DelayDaysType = "AchHold",
                OriginatorAccount = "MERCHANT",
                ReceiverAccount = "DynamicSpvFunding"
            }
        ],
        RetryPolicy = new RetryPolicySettings()
        {
            NotEnoughBalanceMaxPeriodInDays = 5,
            TooManyRequestsMaxAttemptsCount = 5
        }
    };

    public static readonly RawFlowTemplate FactoringFinalFlowTemplateOptions = new()
    {
        FlowTemplateCode = "CREATE.FACTORING.FINAL_PAYMENT",
        TemplateName = "Factoring ACH Final Payment Flow",
        Product = "PayNow",
        PaymentSubscription = "SUBSCRIPTION2",
        Version = "1.0",
        Steps =
        [
            new RawFlowTemplateStep()
            {
                Sequence = 1,
                Name = "CollectionToFunding",
                TransactionType = "AchInternal",
                Condition = null,
                ConditionType = "None",
                TransactionAmount =
                [
                    "FinalAmount",
                    "-MerchantFee",
                    "-customerFees" // TBC
                ],
                DelayDaysType = "None",
                OriginatorAccount = "DynamicSpvCollection",
                ReceiverAccount = "COLLECTION",
                RetryPolicy = "Exponential4Times"
            },
            new RawFlowTemplateStep()
            {
                Sequence = 2,
                Name = "PushToMerchantFinalAmount",
                TransactionType = "AchPush",
                Condition = "CollectionToFunding",
                ConditionType = "Wait",
                TransactionAmount =
                [
                    "FinalAmount",
                    "-MerchantFee",
                    "-customerFees" // TBC
                ],
                DelayDaysType = "None",
                OriginatorAccount = "MERCHANT",
                ReceiverAccount = "DynamicSpvCollection",
                RetryPolicy =  "Exponential4Times"
                }
        ]
    };

    public static readonly RawFlowTemplate DisbursementPaymentTemplateOptions = new()
    {
        FlowTemplateCode = "CREATE.PAYNOW.DISBURSEMENT.V2",
        TemplateName = "PayNow ACH Disbursement Flow v2",
        Product = "PayNow",
        PaymentSubscription = "SUBSCRIPTION1",
        Version = "2.0",
        Steps =
        [
            new RawFlowTemplateStep()
            {
                Sequence = 1,
                Name = "PushToMerchantFullAmount",
                TransactionType = "AchPush",
                ConditionType = "None",
                Condition = null,
                DelayDaysType = "MerchantAchDelay",
                TransactionAmount =
                [
                    "PaymentRequestAmount",
                    "-MerchantFee"
                ],
                OriginatorAccount = "MERCHANT", // to
                ReceiverAccount = "FUNDING", // from
                RetryPolicy = "None"
            }
        ]
    };

    public static readonly RawFlowTemplate FactoringDisbursementFlowTemplateOptions = new()
    {
        FlowTemplateCode = "CREATE.FACTORING.DISBURSEMENT",
        TemplateName = "Factoring ACH Disbursement Flow",
        Product = "PayNow",
        PaymentSubscription = "SUBSCRIPTION2",
        Version = "1.0",
        Steps =
        [
            new RawFlowTemplateStep()
            {
                Sequence = 1,
                Name = "PushFinalPaymentToMerchant",
                TransactionType = "AchPush",
                Condition = null,
                ConditionType = "None",
                TransactionAmount = ["AdvanceAmount"],
                DelayDaysType = "1CD",
                OriginatorAccount = "MERCHANT",
                ReceiverAccount = "DynamicSpvFunding"
            }
        ],
        RetryPolicy = new RetryPolicySettings()
        {
            NotEnoughBalanceMaxPeriodInDays = 5,
            TooManyRequestsMaxAttemptsCount = 5
        }
    };

    public static readonly RawFlowTemplate DrawDisbursementFlowTemplateOptions = new()
    {
        FlowTemplateCode = "CREATE.DRAW.DISBURSEMENT",
        TemplateName = "Draw Disbursement Flow",
        Product = "TradeCredit",
        PaymentSubscription = "SUBSCRIPTION2", //?
        Version = "1.0",
        Steps =
        [
            new RawFlowTemplateStep()
            {
                Sequence = 1,
                Name = "PushToMechantAdvancePayment",
                TransactionType = "AchPull",
                Condition = "Wait",
                ConditionType = "None", //CollectAdvancePaymentToFBO
                TransactionAmount = ["AdvancePaymentAmount", "-MerchantFee"],
                DelayDaysType = "MerchantAchDelay",
                OriginatorAccount = "MERCHANT",
                ReceiverAccount = "COLLECTION",
                RetryPolicy = "Exponential3Times"
            }
        ]
    };

    public static readonly string[] AchFlowTemplateStepNames =
    [
        "PullFromCustomer",
        "CollectToFunding",
        "CollectToRevenue",
        "PushToMerchant",
        "PullFromMerchant",
        "RevenueToCollect",
        "FundingToCollect",
        "PushToMerchantFullAmount",
        "PushToMerchantAdvanceAmount",
    ];

    public static readonly RawFlowTemplateOptions RawTemplateOptions = new()
    {
        Templates =
        [
            InvoicePaymentTemplateOptions,
            DrawRepaymentFlowTemplateOptions,
            FinalPaymentFlowTemplateOptions,
            FinalPaymentV2FlowTemplateOptions,
            FactoringFinalFlowTemplateOptions,
            DisbursementPaymentTemplateOptions,
            InvoicePaymentV2TemplateOptions,
            FactoringDisbursementFlowTemplateOptions,
            DrawDisbursementFlowTemplateOptions,
            InvoicePaymentCardTemplateOptions,
            DrawRepaymentCardFlowTemplateOptions,
            DrawRepaymentManualFlowTemplateOptions,
            ManualPaymentPullFlowTemplateOptions,
            SubscriptionFeePaymentFlowTemplateOptions
        ]
    };
}