﻿using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.ManualPaymentPull;
using BlueTape.PaymentService.CompatibilityService.Extensions;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Operation;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;

namespace BlueTape.PaymentService.CompatibilityService.Services.ManualPaymentPull;

public class ManualPaymentPullOperationSyncService(
    IOperationsRepository operationsRepository,
    IManualPaymentPullTransactionSyncService transactionSyncService,
    ILoanApplicationRepository loanApplicationRepository,
    IManualPaymentPullCompatibilityMapper compatibilityMapper) : IManualPaymentPullOperationSyncService
{
    public async Task PerformOperation(PaymentRequestEntity paymentRequest, CancellationToken cancellationToken)
    {
        var loanApplication =
            await loanApplicationRepository.GetByLmsId(paymentRequest.PaymentRequestDetails?.DrawId?.ToString(),
                cancellationToken);

        var operation = compatibilityMapper.MapFromPaymentRequestToOperation(paymentRequest, loanApplication?.BlueTapeId ?? string.Empty);
        await operationsRepository.InsertMany(new List<OperationEntity>() { operation }, cancellationToken);
        await transactionSyncService.PerformTransactions(paymentRequest, operation, cancellationToken);
    }

    public async Task SyncOperation(PaymentRequestEntity paymentRequest, CancellationToken cancellationToken)
    {
        var existingOperation =
           (await operationsRepository.GetByPaymentRequestId(paymentRequest.Id.ToString(), cancellationToken)).LastOrDefault();
        if (existingOperation == null)
        {
            await PerformOperation(paymentRequest, cancellationToken);
        }

        string? ownerId = null;
        if (string.IsNullOrEmpty(existingOperation?.OwnerId))
        {
            var loanApplication =
                await loanApplicationRepository.GetByLmsId(paymentRequest.PaymentRequestDetails?.DrawId?.ToString(),
                    cancellationToken);

            ownerId = loanApplication?.BlueTapeId;
        }

        var updateOperationEntity = GetUpdateOperationEntity(paymentRequest, ownerId);
        await operationsRepository.Update(paymentRequest.Id.ToString(), updateOperationEntity, cancellationToken);

        await transactionSyncService.SyncTransactions(paymentRequest, cancellationToken);
    }

    private UpdateOperationEntity GetUpdateOperationEntity(PaymentRequestEntity paymentRequest, string? ownerId)
    {
        var pullResult = paymentRequest.Transactions.FirstOrDefault(x => x.Status == TransactionStatus.Cleared)?.UpdatedAt;
        var updateRequest = new UpdateOperationEntity()
        {
            Date = paymentRequest.Date.ToDateTime(new TimeOnly(), DateTimeKind.Utc),
            Status = paymentRequest.GetPaymentOperationStatus().ToString(),
            PaymentMethod = PaymentMethod.Ach.ToString().ToLower(),
            PayerId = paymentRequest.PayerId,
            PayeeId = paymentRequest.PayeeId,
            FirstTransactionDate = paymentRequest.GetFirstTransactionDate(),
            PaymentRequestId = paymentRequest.Id.ToString(),
            PullResult = pullResult,
            OwnerId = ownerId,
            LmsPaymentId = paymentRequest.PaymentRequestDetails?.LMSPaymentId.ToString()
        };

        return updateRequest;
    }
}
