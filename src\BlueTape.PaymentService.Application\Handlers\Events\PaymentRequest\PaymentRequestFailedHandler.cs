﻿using BlueTape.LinqpalClient.Abstractions;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Abstractions.Services.Notification;
using BlueTape.PaymentService.Application.Models.Events.PaymentRequest;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.UnitOfWork.Abstractions;
using BlueTape.SNS.SlackNotification.Models;
using MediatR;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.Application.Handlers.Events.PaymentRequest;

public class PaymentRequestFailedHandler(
    IUnitOfWork unitOfWork,
    ILoanManagementService loanManagementService,
    ILinqpalHttpClient nodeJsHttpClient,
    ISlackNotificationService notificationService,
    ILogger<PaymentRequestFailedHandler> logger) : BasePaymentRequestHandler(unitOfWork, logger),
    IRequestHandler<PaymentRequestFailedEvent>
{
    protected override IEnumerable<PaymentRequestStatus> AllowedPaymentRequestStatuses => new[]
    {

        PaymentRequestStatus.Failed, PaymentRequestStatus.Aborted
    };

    public Task Handle(PaymentRequestFailedEvent request, CancellationToken cancellationToken)
    {
        return base.Handle(request, cancellationToken);
    }

    protected override async Task HandleAch(PaymentRequestEntity paymentRequest, CancellationToken ct)
    {
        switch (paymentRequest.FlowTemplateCode)
        {
            case DomainConstants.DrawRepayment:
                await HandleAchDrawRepayment(paymentRequest, ct);
                break;
            case DomainConstants.DrawRepaymentCard:
            case DomainConstants.DrawRepaymentManual:
                await HandleAchDrawRepaymentInternal(paymentRequest, ct);
                break;
            case DomainConstants.InvoicePaymentCard:
            case DomainConstants.InvoicePayment:
            case DomainConstants.InvoiceDisbursementV2:
                await HandleAchPayNow(paymentRequest, ct);
                break;
            case DomainConstants.InvoicePaymentV2:
                await HandleAchInvoicePaymentV2(paymentRequest, ct);
                break;
            case DomainConstants.FinalPayment:
            case DomainConstants.FinalPaymentV2:
            case DomainConstants.FactoringFinalPayment:
                break;
            default:
                logger.LogWarning($"Unable to handle payment request of type: {paymentRequest.FlowTemplateCode}, paymentRequestId: {paymentRequest.Id}");
                break;
        }
    }

    private Task HandleAchPayNow(PaymentRequestEntity paymentRequest, CancellationToken ct)
    {
        return Task.CompletedTask;
    }

    private async Task HandleAchInvoicePaymentV2(PaymentRequestEntity paymentRequest, CancellationToken ct)
    {
        var lmsPaymentId = paymentRequest.PaymentRequestDetails!.LMSPaymentId;
        await loanManagementService.RejectLmsPayment(paymentRequest.Id, lmsPaymentId, ct);
    }

    private async Task HandleAchDrawRepayment(PaymentRequestEntity paymentRequest, CancellationToken ct)
    {
        var transaction = paymentRequest.Transactions.FirstOrDefault();

        try
        {
            // Run recalled NJS logic if we got any R** code
            if (!string.IsNullOrEmpty(transaction!.LastResultCode) &&
                transaction.LastResultCode.StartsWith("R", StringComparison.OrdinalIgnoreCase))
                await nodeJsHttpClient.SendTransactionIsRecalled(transaction.ReferenceNumber, ct);

            var lmsPaymentId = paymentRequest.PaymentRequestDetails!.LMSPaymentId;
            await loanManagementService.RejectLmsPayment(paymentRequest.Id, lmsPaymentId, ct);
        }
        catch (Exception ex)
        {
            await notificationService.Notify($"Internal Error was happened during LMS payment rejection. Possible the payment already rejected Message: {ex.Message}",
                "LMS Payment Rejection Error",
                EventLevel.Warning,
                CancellationToken.None);
        }
    }

    private async Task HandleAchDrawRepaymentInternal(PaymentRequestEntity paymentRequest, CancellationToken ct)
    {
        try
        {
            var lmsPaymentId = paymentRequest.PaymentRequestDetails!.LMSPaymentId;
            await loanManagementService.RejectLmsPayment(paymentRequest.Id, lmsPaymentId, ct);
        }
        catch (Exception ex)
        {
            await notificationService.Notify($"Internal Error was happened during LMS payment rejection. Possible the payment already rejected Message: {ex.Message}",
                "LMS Payment Rejection Error",
                EventLevel.Warning,
                CancellationToken.None);
        }
    }
}
