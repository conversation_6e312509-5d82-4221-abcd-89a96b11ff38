using Azure.Data.Tables;
using BlueTape.CompanyClient.Abstractions;
using BlueTape.Integrations.Aion.AzureTableStorage.Abstractions;
using BlueTape.Integrations.Aion.AzureTableStorage.Entities;
using BlueTape.Integrations.Aion.Infrastructure.Constants;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.PaymentService.Application.Abstractions.Processors;
using BlueTape.PaymentService.Application.Abstractions.Senders.Payments;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.DataAccess.External.Abstractions.Services;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Company;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.CustomerAccount;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Invoice;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.UserRole;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Entities.Filters;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.BaseDisbursement;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepaymentCard;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepaymentManual;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.InvoicePayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.SubscriptionFeePayment;
using BlueTape.PaymentService.TestUtilities.Abstractions.Senders;
using BlueTape.PaymentService.TestUtilities.Abstractions.Services;
using BlueTape.PaymentService.TestUtilities.Models;
using BlueTape.PaymentService.UnitOfWork.Abstractions;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Utilities.Extensions;
using System.Diagnostics.CodeAnalysis;
using System.Security.Cryptography;
using System.Text.Json;
using TinyHelpers.Extensions;

namespace BlueTape.PaymentService.TestUtilities.Services;

[ExcludeFromCodeCoverage(Justification = "The service was created to simplify the testing of payments. It should not be covered by tests")]
public class TestingService(
    IInvoiceRepository invoiceRepository,
    ICustomerRepository customerRepository,
    IUserRepository userRepository,
    IUserRoleRepository userRoleRepository,
    ILoanApplicationRepository loanApplicationRepository,
    IInvoicePaymentMessageSender messageSender,
    ICompanyHttpClient companyHttpClient,
    ICompaniesRepository companiesRepository,
    IPaymentRequestService paymentRequestService,
    IAzureStorageTransactionRepository azureRepository,
    IPaymentJobProcessor paymentJobProcessor,
    IDisbursementMessageSender disbursementMessageSender,
    IDrawRepaymentMessageSender drawRepaymentMessageSender,
    IDrawRepaymentCardMessageSender drawRepaymentCardMessageSender,
    IDrawRepaymentManualMessageSender drawRepaymentManualMessageSender,
    IPaymentTransactionService paymentPaymentTransactionService,
    ITransactionStatusUpdateProcessor transactionStatusUpdateProcessor,
    ICommandManagementService commandManagementService,
    ILmsExternalService lmsService,
    IUnitOfWork unitOfWork) : ITestingService
{

    public async Task<InvoiceEntity> CreateInvoice(string? supplierEmail, string? customerEmail, double? amount, CancellationToken ct)
    {
        var (supplierRole, customer, company) = await GetCommonEntities(supplierEmail, customerEmail, ct);
        amount ??= 101.01;

        return await CreateInvoice(supplierRole, customer, null, amount.Value, ct);
    }

    public async Task<InvoicePaymentRequestMessage?> SendInvoicePaymentV2RequestMessage(
        CreateInvoicePaymentV2Request request, string userId, PaymentRequestType requestType, CancellationToken ct)
    {
        if (requestType == PaymentRequestType.InvoicePaymentV2)
        {
            if (!Guid.TryParse(request.DrawId, out var parcedDrawId))
                throw new ArgumentException("Invalid draw id");

            var loan = await lmsService.GetLoanDetailedById(parcedDrawId, ct);
            if (loan?.CompanyId is null)
                throw new ArgumentException("Loan not found");

            var bankAccounts = await companyHttpClient.GetBankAccountsByCompanyIdAsync(loan!.CompanyId, ct);

            var message = new InvoicePaymentRequestMessage()
            {
                FlowTemplateCode = DomainConstants.InvoicePaymentV2,
                CreatedBy = "BlueTape Payment Test Service",
                BlueTapeCorrelationId = Guid.NewGuid().ToString(),
                PaymentRequestDetails = new()
                {
                    Date = DateTime.Now,
                    Currency = "USD",
                    RequestedAmount = request.Amount,
                    PaymentMethod = nameof(PaymentMethod.Ach),
                    CustomerDetails = new CustomerDetails
                    {
                        Id = request.Receiver?.CompanyId ?? loan.CompanyId,
                        Name = "TestName",
                        AccountId = bankAccounts[0].Id,
                    },
                    SellerDetails = new SellerDetails
                    {
                        CompanyId = loan.MerchantId ?? loan.CompanyId,
                        Name = loan.MerchantName ?? string.Empty,
                    },
                    /*ManualPaymentDetails = new()
                    {
                        ManualAccountCode = request?.OriginatorAccountCode.ToString(),
                    },*/
                    PayablesDetails =
                    [
                        new()
                        {
                            Id = loan.LoanPayables?[0].PayableId ?? string.Empty,
                            PayableType = "Invoice",
                            RequestedAmount = loan.Amount
                        }
                    ],
                },

            };

            await messageSender.SendMessage(new ServiceBusMessageBt<InvoicePaymentRequestMessage>(message), ct);

            return message;
        }

        return null;
    }

    public async Task CreateBunchOfPaymentRequests(string? supplierEmail, string? customerEmail, double? amount, CancellationToken ct)
    {
        await CreatePaymentRequest(supplierEmail, customerEmail, amount, invoiceId: null, ct);
        // 2 invoice payments will be created by this method
        amount += 0.01;
        await CreatePaymentRequestV2Flow(supplierEmail, customerEmail, amount, invoiceId: null, ct);
        amount += 0.01;
        await CreateDisbursementPayment(supplierEmail, customerEmail, amount, PaymentRequestType.FinalPayment, invoiceId: null, ct);
        amount += 0.01;
        await CreateDisbursementPayment(supplierEmail, customerEmail, amount, PaymentRequestType.FactoringDisbursement, invoiceId: null, ct);
        amount += 0.01;
        await CreateDisbursementPayment(supplierEmail, customerEmail, amount, PaymentRequestType.FactoringFinalPayment, invoiceId: null, ct);
        amount += 0.01;
        await CreateDrawRepayment(supplierEmail, customerEmail, amount, invoiceId: null, ct);
        amount += 0.01;
        await CreateSubscriptionFeePayment(supplierEmail, customerEmail, amount, invoiceId: null, ct);
    }

    public async Task<string> CreatePaymentRequest(string? supplierEmail, string? customerEmail, double? amount, string? invoiceId, CancellationToken ct, bool onlyMessage = false)
    {
        if (string.IsNullOrEmpty(supplierEmail) || string.IsNullOrEmpty(customerEmail) || !amount.HasValue)
            throw new ArgumentException("Invalid request arguments");

        var message = await CreatePayNowPaymentRequestMessage(supplierEmail, customerEmail, amount, DomainConstants.InvoicePayment, ct);
        if (!onlyMessage)
        {
            await messageSender.SendMessage(new ServiceBusMessageBt<InvoicePaymentRequestMessage>(message), ct);
        }

        return JsonSerializer.Serialize(message);
    }

    public async Task<string> CreatePaymentRequestV2Flow(string? supplierEmail, string? customerEmail, double? amount, string? invoiceId, CancellationToken ct, bool onlyMessage = false)
    {
        if (string.IsNullOrEmpty(supplierEmail) || string.IsNullOrEmpty(customerEmail) || !amount.HasValue)
            throw new ArgumentException("Invalid request arguments");

        var message = await CreatePayNowPaymentRequestMessage(supplierEmail, customerEmail, amount, DomainConstants.InvoicePaymentV2, ct);

        if (!onlyMessage)
        {
            await messageSender.SendMessage(new ServiceBusMessageBt<InvoicePaymentRequestMessage>(message), ct);
            message.FlowTemplateCode = DomainConstants.InvoiceDisbursementV2;
            await messageSender.SendMessage(new ServiceBusMessageBt<InvoicePaymentRequestMessage>(message), ct);
        }

        return JsonSerializer.Serialize(message);
    }

    public async Task<string> CreateFactoringFlow(string? supplierEmail, string? customerEmail, double? amount, CancellationToken ct)
    {
        if (string.IsNullOrEmpty(supplierEmail) || string.IsNullOrEmpty(customerEmail) || !amount.HasValue)
            throw new ArgumentException("Invalid request arguments");

        var message = await CreatePayNowPaymentRequestMessage(supplierEmail, customerEmail, amount, DomainConstants.InvoicePaymentV2, ct);
        await messageSender.SendMessage(new ServiceBusMessageBt<InvoicePaymentRequestMessage>(message), ct);

        var disbursementMessage = new BaseDisbursementRequestMessage()
        {
            FlowTemplateCode = DomainConstants.FactoringDisbursement,
            CreatedBy = "BlueTapeTests",
            BlueTapeCorrelationId = Guid.NewGuid().ToString(),
            PaymentRequestDetails = new BaseDisbursementRequestDetails()
            {
                Date = DateTime.Now,
                Currency = "USD",
                RequestedAmount = (decimal)amount * (decimal)0.8,
                PaymentMethod = "ach",
                DrawDetails = new BaseDisbursementDrawDetails
                {
                    Id = Guid.NewGuid(),
                },
                PayablesDetails = message.PaymentRequestDetails.PayablesDetails,
                SellerDetails = message.PaymentRequestDetails.SellerDetails
            }
        };

        await disbursementMessageSender.SendMessage(new ServiceBusMessageBt<BaseDisbursementRequestMessage>(disbursementMessage), ct);

        var finalPaymentMessage = new BaseDisbursementRequestMessage()
        {
            FlowTemplateCode = DomainConstants.FactoringFinalPayment,
            CreatedBy = "BlueTapeTests",
            BlueTapeCorrelationId = Guid.NewGuid().ToString(),
            PaymentRequestDetails = new BaseDisbursementRequestDetails()
            {
                Date = DateTime.Now,
                Currency = "USD",
                RequestedAmount = (decimal)amount * (decimal)0.2,
                PaymentMethod = "ach",
                DrawDetails = new BaseDisbursementDrawDetails
                {
                    Id = Guid.NewGuid(),
                },
                PayablesDetails = message.PaymentRequestDetails.PayablesDetails,
                SellerDetails = message.PaymentRequestDetails.SellerDetails
            }
        };

        await disbursementMessageSender.SendMessage(new ServiceBusMessageBt<BaseDisbursementRequestMessage>(finalPaymentMessage), ct);

        return JsonSerializer.Serialize(message);
    }

    public async Task<string> CreateTradeCreditFlow(string? supplierEmail, string? customerEmail, double? amount, CancellationToken ct)
    {
        await CreateDrawRepayment(supplierEmail, customerEmail, amount, invoiceId: null, ct);

        await CreateDisbursementPayment(supplierEmail, customerEmail, amount, PaymentRequestType.DrawDisbursement, invoiceId: null, ct);

        await CreateDisbursementPayment(supplierEmail, customerEmail, amount, PaymentRequestType.FinalPayment, invoiceId: null, ct);

        return string.Empty;
    }

    public async Task<BaseDisbursementRequestMessage> CreateDisbursementPayment(string? supplierEmail, string? customerEmail, double? amount, PaymentRequestType type, string? invoiceId, CancellationToken ct, bool onlyMessage = false)
    {
        var flowTemplateCode = type switch
        {
            PaymentRequestType.FinalPayment => DomainConstants.FinalPayment,
            PaymentRequestType.FinalPaymentV2 => DomainConstants.FinalPaymentV2,
            PaymentRequestType.FactoringDisbursement => DomainConstants.FactoringDisbursement,
            PaymentRequestType.FactoringFinalPayment => DomainConstants.FactoringFinalPayment,
            PaymentRequestType.DrawDisbursement => DomainConstants.DrawDisbursement,
            _ => throw new ArgumentException("Invalid request type", nameof(type))
        };

        var (supplierRole, customer, company) = await GetCommonEntities(supplierEmail, customerEmail, ct);
        amount ??= RandomNumberGenerator.GetInt32(0, 200);
        var invoice = await CreateInvoice(supplierRole, customer, company, amount.Value, ct);
        var loanApplication = await loanApplicationRepository.GetFirstApprovedLegacyLoanByCompany(company.Id, ct);


        var message = new BaseDisbursementRequestMessage()
        {
            FlowTemplateCode = flowTemplateCode,
            CreatedBy = "BlueTapeTests",
            BlueTapeCorrelationId = Guid.NewGuid().ToString(),
            PaymentRequestDetails = new BaseDisbursementRequestDetails()
            {
                Date = DateTime.Now,
                Currency = "USD",
                RequestedAmount = (decimal)amount,
                PaymentMethod = "ach",
                ExecuteAfter = DateTime.UtcNow.AddDays(-1),
                PayablesDetails = new List<PayablesDetail>
                {
                    new()
                    {
                        Id = invoice.Id,
                        PayableType = "Invoice",
                        PayableAmount = (decimal)amount,
                        RequestedAmount = (decimal)amount
                    },
                },
                CustomerDetails = new CustomerDetails
                {
                    Id = company.Id,
                    Name = customer.Name!,
                },
                SellerDetails = new SellerDetails
                {
                    CompanyId = supplierRole.CompanyId,
                    Name = "Acme Lumber",
                    PaymentSettings = new PaymentSettings
                    {
                        MerchantAchDelayDays = 0
                    }
                },
            }
        };

        if (!onlyMessage)
        {
            await disbursementMessageSender.SendMessage(
                new ServiceBusMessageBt<BaseDisbursementRequestMessage>(message), ct);
        }

        return message;
    }

    public async Task<SubscriptionFeePaymentRequestMessage> CreateSubscriptionFeePayment(string? supplierEmail, string? customerEmail, double? amount, string? invoiceId, CancellationToken ct, bool onlyMessage = false)
    {
        if (string.IsNullOrEmpty(supplierEmail) || string.IsNullOrEmpty(customerEmail) || !amount.HasValue)
            throw new ArgumentException("Invalid request arguments");

        var (supplierRole, customer, company) = await GetCommonEntities(supplierEmail, customerEmail, ct);
        var bankAccounts = await companyHttpClient.GetBankAccountsByCompanyIdAsync(company.Id, ct);

        var message = new SubscriptionFeePaymentRequestMessage
        {
            FlowTemplateCode = DomainConstants.SubscriptionFeePayment,
            CreatedBy = "BlueTapeTests",
            BlueTapeCorrelationId = Guid.NewGuid().ToString(),
            PaymentRequestDetails = new SubscriptionFeePaymentRequestDetails
            {
                Date = DateTime.Now,
                Currency = "USD",
                RequestedAmount = (decimal)amount,
                PaymentMethod = "Ach",
                ConfirmationType = ConfirmationType.None,
                CustomerDetails = new CustomerDetails
                {
                    Id = company.Id,
                    Name = customer.Name!,
                    AccountId = bankAccounts[0].Id
                },
                AdditionalDetails = new AdditionalDetails
                {
                    Reason = "Subscription fee payment",
                    InvoiceNumber = $"INV-{DateTime.Now:yyyyMMdd}-{RandomNumberGenerator.GetInt32(1000, 9999)}"
                }
            }
        };
        return message;
    }

    public async Task<DrawRepaymentRequestMessage> CreateDrawRepayment(string? supplierEmail, string? customerEmail, double? amount, string? invoiceId, CancellationToken ct, bool onlyMessage = false)
    {
        var (supplierRole, customer, company) = await GetCommonEntities(supplierEmail, customerEmail, ct);
        amount ??= RandomNumberGenerator.GetInt32(0, 200);
        var bankAccounts = await companyHttpClient.GetBankAccountsByCompanyIdAsync(company.Id, ct);
        var loanApplication = await loanApplicationRepository.GetFirstApprovedLegacyLoanByCompany(company.Id, ct);

        var message = new DrawRepaymentRequestMessage()
        {
            FlowTemplateCode = DomainConstants.DrawRepayment,
            CreatedBy = "BlueTapeTests",
            BlueTapeCorrelationId = Guid.NewGuid().ToString(),
            PaymentRequestDetails = new DrawRepaymentRequestDetails()
            {
                Date = DateTime.Now,
                Currency = "USD",
                RequestedAmount = (decimal)amount,
                PaymentMethod = "ach",
                DrawRepaymentDrawDetails = new DrawRepaymentDrawDetails()
                {
                    Id = Guid.Parse(loanApplication.LmsId!),
                    Amount = (decimal)amount,
                },
                CustomerDetails = new CustomerDetails
                {
                    Id = company.Id,
                    Name = customer.Name!,
                    AccountId = bankAccounts.First().Id,
                },
                CompatibilityDetails = new CompatibilityDetails
                {
                    LmsPaymentId = Guid.NewGuid().ToString(),
                }
            }
        };

        if (!onlyMessage)
        {
            await drawRepaymentMessageSender.SendMessage(new ServiceBusMessageBt<DrawRepaymentRequestMessage>(message),
                ct);
        }

        return message;
    }

    public async Task<DrawRepaymentManualRequestMessage> CreateDrawRepaymentManual(string supplierEmail,
        string customerEmail, double? amount, string? invoiceId, CancellationToken ct, bool onlyMessage = false)
    {
        var (supplierRole, customer, company) = await GetCommonEntities(supplierEmail, customerEmail, ct);
        var bankAccounts = await companyHttpClient.GetBankAccountsByCompanyIdAsync(company.Id, ct);
        var loanApplication = (await loanApplicationRepository.GetByInvoicesIds([invoiceId], ct)).FirstOrDefault();
        amount ??= RandomNumberGenerator.GetInt32(0, 200);

        var message = new DrawRepaymentManualRequestMessage()
        {
            FlowTemplateCode = DomainConstants.DrawRepaymentManual,
            CreatedBy = "BlueTapeTests",
            BlueTapeCorrelationId = Guid.NewGuid().ToString(),
            PaymentRequestDetails = new()
            {
                Date = DateTime.Now,
                Currency = "USD",
                RequestedAmount = (decimal)amount,
                PaymentMethod = "ach",
                DrawDetails = new()
                {
                    Id = Guid.Parse(loanApplication?.LmsId!),
                    Amount = (decimal)(amount)
                },
                CustomerDetails = new CustomerDetails
                {
                    Id = company.Id,
                    Name = customer.Name!,
                    AccountId = bankAccounts.First().Id
                },
                ManualPaymentDetails = new()
                {
                    ManualPaymentMethod = ManualPaymentMethod.Ach.ToString(),
                    ManualAccountCode = AccountCodeType.LOCKBOXCOLLECTION.ToString(),
                    ExternalReferenceNumber = "TESTREFERENCE",
                    UserId = "TESTUSER",
                },
            }
        };

        if (!onlyMessage)
        {
            await drawRepaymentManualMessageSender.SendMessage(new ServiceBusMessageBt<DrawRepaymentManualRequestMessage>(message),
                ct);
        }

        return message;
    }

    public async Task<DrawRepaymentCardRequestMessage> CreateDrawRepaymentCard(string supplierEmail,
    string customerEmail, double? amount, string? invoiceId, CancellationToken ct, bool onlyMessage = false)
    {
        var (supplierRole, customer, company) = await GetCommonEntities(supplierEmail, customerEmail, ct);
        var bankAccounts = await companyHttpClient.GetBankAccountsByCompanyIdAsync(company.Id, ct);
        var loanApplication = (await loanApplicationRepository.GetByInvoicesIds([invoiceId], ct)).FirstOrDefault();
        amount ??= RandomNumberGenerator.GetInt32(0, 200);

        var message = new DrawRepaymentCardRequestMessage()
        {
            FlowTemplateCode = DomainConstants.DrawRepaymentCard,
            CreatedBy = "BlueTapeTests",
            BlueTapeCorrelationId = Guid.NewGuid().ToString(),
            PaymentRequestDetails = new()
            {
                Date = DateTime.Now,
                Currency = "USD",
                RequestedAmount = (decimal)amount,
                PaymentMethod = "ach",
                DrawRepaymentDrawDetails = new()
                {
                    Id = Guid.Parse(loanApplication?.LmsId!),
                    Amount = (decimal)(amount)
                },
                CustomerDetails = new CustomerDetails
                {
                    Id = company.Id,
                    Name = customer.Name!,
                    AccountId = bankAccounts.First().Id
                },
                CompatibilityDetails = new CompatibilityDetails
                {
                    LmsPaymentId = "50111f79-7390-472d-bc23-6d1f2b16d924",
                },
                CardPaymentDetails = new()
                {
                    TransactionId = "dlj807sdjg9725",
                }
            }
        };

        if (!onlyMessage)
        {
            await drawRepaymentCardMessageSender.SendMessage(new ServiceBusMessageBt<DrawRepaymentCardRequestMessage>(message),
                ct);
        }

        return message;
    }

    public async Task<string> InitializeStatusMovementForBanchOfPayments(CancellationToken ct)
    {
        var paymentRequests =
            (await paymentRequestService.GetByFilter(
                new PaymentRequestFilter() { DateFrom = DateOnly.FromDateTime(DateTime.Now.AddDays(-7)) }, ct))
            .Result
            .Where(x => x.Status != PaymentRequestStatus.Settled)
            .ToList();

        if (paymentRequests.IsNullOrEmpty())
            return "No payment requests available for status movement";

        foreach (var paymentRequest in paymentRequests)
        {
            await InitializeAionTransactionsStatusMovement(paymentRequest, TransactionStatus.Cleared, ct);
        }

        await transactionStatusUpdateProcessor.Process(ct);
        await paymentJobProcessor.Process(ct);

        return $"Status movement done, affected payment requests: {paymentRequests.Count}";
    }

    public async Task<string> InitializeAionTransactionsStatusMovement(Guid? paymentRequestId, TransactionStatus status, CancellationToken ct)
    {
        var paymentRequest = await paymentRequestService.GetById(paymentRequestId!.Value, ct);

        var message = await InitializeAionTransactionsStatusMovement(paymentRequest, status, ct);

        await transactionStatusUpdateProcessor.Process(ct);
        await paymentJobProcessor.Process(ct);

        return message;
    }

    private async Task<string> InitializeAionTransactionsStatusMovement(PaymentRequestModel? paymentRequest, TransactionStatus status, CancellationToken ct)
    {
        if (paymentRequest == null)
            return "Payment request with the Id is not exists";

        if (paymentRequest.Status == PaymentRequestStatus.Settled)
            return "Payment request with the Id has SETTED status";

        var transaction = paymentRequest.Transactions
            .Where(e => e.Status != TransactionStatus.Placed || e.SequenceNumber == 1)
            .OrderByDescending(x => x.SequenceNumber)
            .ThenByDescending(e => e.CreatedAt)
            .FirstOrDefault();

        string message = string.Empty;
        switch (transaction!.Status)
        {
            case TransactionStatus.Cleared:
            case TransactionStatus.Placed:
                var command = paymentRequest.PaymentRequestCommands.FirstOrDefault(x => x.TransactionId == transaction.Id);

                if (command is null)
                    break;

                await commandManagementService.ManageCommand(command.Id, ct);
                break;
            case TransactionStatus.Processing:
            case TransactionStatus.Processed:
                var newAionStatus = MapAionStatus(status);
                await MoveAzureStorageStatus(transaction, newAionStatus, ct);
                break;
            case TransactionStatus.Failed:
            case TransactionStatus.Error:
                await paymentPaymentTransactionService.RetryFailedTransaction(paymentRequest.Id, transaction.Id, "testExecution", ct);
                message = "Transaction has been retried.";
                break;
            default:
                message = "Unexpected status transaction status.";
                break;
        }

        message += $" Current transaction status: {transaction.Status}, sequence number: {transaction.SequenceNumber}";

        return message;
    }

    private string MapAionStatus(TransactionStatus status)
    {
        return status switch
        {
            TransactionStatus.Failed or TransactionStatus.Error => AionStatuses.Error,
            _ => AionStatuses.Cleared
        };
    }

    private async Task MoveAzureStorageStatus(PaymentTransactionModel transaction, string newStatus, CancellationToken ct)
    {
        var azTransactions = (await azureRepository.GetByBlueTapeTransactionNumbersAsync([transaction.TransactionNumber], ct)).ToList();

        TransactionEntity? azTransaction = null;

        if (azTransactions.IsNullOrEmpty())
        {
            azTransaction = new TransactionEntity
            {
                AionTransactionNumber = $"A{Guid.NewGuid()}",
                IsProcessed = false,
                ErrorCode = string.Empty,
                BlueTapeTransactionNumber = transaction.TransactionNumber!,
                AionTransactionType = transaction.TransactionType == PaymentTransactionType.AchInternal ? AionTransactionType.INTERNAL : AionTransactionType.EXTERNAL,
                EffectiveDate = null,
                TransferType = null,
                CreatedAt = DateTime.Now.ToUniversalTime()
            };
        }

        azTransaction ??= azTransactions.FirstOrDefault();
        azTransaction!.IsProcessed = false;
        azTransaction.UpdatedAt = DateTime.Now.ToUniversalTime();

        if (azTransaction.AionTransactionStatus.IsNullOrEmpty())
        {
            azTransaction.AionTransactionStatus = AionStatuses.Processing;
            await azureRepository.InsertOrReplaceEntityAsync(azTransaction, TableUpdateMode.Merge, ct);
        }

        azTransaction.AionTransactionStatus = newStatus;

        await azureRepository.InsertOrReplaceEntityAsync(azTransaction, TableUpdateMode.Merge, ct);
    }

    private async Task<InvoicePaymentRequestMessage> CreatePayNowPaymentRequestMessage(string supplierEmail,
        string customerEmail, double? amount, string flowTemplateCode, CancellationToken ct)
    {
        var (supplierRole, customer, company) = await GetCommonEntities(supplierEmail, customerEmail, ct);
        var bankAccounts = await companyHttpClient.GetBankAccountsByCompanyIdAsync(company.Id, ct);
        amount ??= RandomNumberGenerator.GetInt32(0, 200);

        var invoice = await CreateInvoice(supplierRole, customer, company, amount.Value, ct);

        return new InvoicePaymentRequestMessage
        {
            FlowTemplateCode = flowTemplateCode,
            CreatedBy = "BlueTapeTests",
            BlueTapeCorrelationId = Guid.NewGuid().ToString(),
            PaymentRequestDetails = new InvoicePaymentRequestDetails
            {
                Date = DateTime.Now,
                Currency = "USD",
                RequestedAmount = (decimal)amount,
                PaymentMethod = "ach",
                PayablesDetails =
                [
                    new()
                    {
                        Id = invoice.Id,
                        PayableType = "Invoice",
                        PayableAmount = (decimal)amount,
                        RequestedAmount = (decimal)amount
                    }
                ],
                CustomerDetails = new CustomerDetails
                {
                    Id = company.Id,
                    Name = customer.Name!,
                    AccountId = bankAccounts.First().Id
                },
                SellerDetails = new SellerDetails
                {
                    CompanyId = supplierRole.CompanyId,
                    Name = "Acme Lumber",
                    PaymentSettings = new PaymentSettings
                    {
                        MerchantAchDelayDays = 0
                    }
                },
                FeeDetails =
                [
                    new()
                    {
                        CompanyId = supplierRole.CompanyId,
                        Amount = 1,
                        Description = "Merchant fee",
                        Type = "Merchant"
                    }
                ],
                ProjectDetails = new ProjectDetails
                {
                    Id = "project_id"
                }
            }
        };
    }

    private async Task<(UserRoleEntity supplierRole, CustomerEntity customer, CompanyEntity company)> GetCommonEntities(string? supplierEmail, string? customerEmail, CancellationToken ct)
    {
        if (string.IsNullOrEmpty(supplierEmail) && string.IsNullOrEmpty(customerEmail))
            throw new ArgumentException("User emails is null");

        var users = await userRepository.GetByLoginInformationAsync([supplierEmail!], ct);
        if (users.IsNullOrEmpty()) return (null, null, null)!;

        var supplierUser = users.FirstOrDefault(x => x.Login == supplierEmail);
        if (supplierUser == null)
            throw new ArgumentException("User not found");

        var supplierRoles = await userRoleRepository.GetByExternalIds([supplierUser.ExternalId], ct);
        var supplierRole = supplierRoles.Where(x => x.Role == "Owner").MaxBy(x => x.CreatedAt);
        var customer = await customerRepository.GetByEmail(customerEmail!, ct);
        var company = await companiesRepository.GetByName(customer!.Name!, ct);

        return (supplierRole, customer, company)!;
    }

    private async Task<InvoiceEntity> CreateInvoice(UserRoleEntity supplierRole, CustomerEntity customer, CompanyEntity? company, double amount, CancellationToken ct)
    {
        var invoiceNumber = $"Test-{RandomNumberGenerator.GetInt32(0, 99999)}";

        var newInvoice = new InvoiceEntity()
        {
            CompanyId = supplierRole.CompanyId,
            Type = "invoice",
            InvoiceNumber = invoiceNumber,
            QuoteNumber = null,
            OperationId = "",
            PayerId = company?.Id ?? "",
            CustomerAccountId = customer.BlueTapeCustomerId,
            SupplierInvitationDetails = null,
            InvoiceDate = DateTime.UtcNow,
            MaterialSubtotal = amount,
            InvoiceDueDate = DateTime.UtcNow,
            AddressType = "Service",
            InvoiceNotificationType = "Both",
            Status = "PLACED",
            Approved = true,
            TakenById = null,
            TotalAmount = (decimal)amount,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
        };

        return await invoiceRepository.Create(newInvoice, ct);
    }

    public async Task<string> ChangePaymentSubscription(Guid paymentRequestId, PaymentSubscriptionType newSubscriptionType, CancellationToken ct)
    {
        try
        {
            var paymentRequestEntity = await unitOfWork.GetById<PaymentRequestEntity>(paymentRequestId, ct);

            if (paymentRequestEntity == null)
            {
                return $"Payment request with ID {paymentRequestId} not found";
            }

            var oldSubscriptionType = paymentRequestEntity.PaymentSubscription;
            paymentRequestEntity.PaymentSubscription = newSubscriptionType;

            await unitOfWork.UpdatePaymentRequest(paymentRequestEntity, ct);

            return $"Successfully changed payment subscription for request {paymentRequestId} from {oldSubscriptionType} to {newSubscriptionType}";
        }
        catch (Exception ex)
        {
            return $"Error changing payment subscription for request {paymentRequestId}: {ex.Message}";
        }
    }
}


