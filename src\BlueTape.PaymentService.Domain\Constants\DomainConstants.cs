﻿namespace BlueTape.PaymentService.Domain.Constants;

public static class DomainConstants
{
    public const string PaymentService = "BlueTape.PaymentService";

    public const string InvoicePayment = "CREATE.PAYNOW.INVOICE_PAYMENT";
    public const string DrawDisbursement = "CREATE.DRAW.DISBURSEMENT";
    public const string DrawRepayment = "CREATE.DRAW.REPAYMENT";
    public const string DrawRepaymentCard = "CREATE.DRAW.REPAYMENT.CARD";
    public const string DrawRepaymentManual = "CREATE.DRAW.REPAYMENT.MANUAL";
    public const string FinalPayment = "CREATE.DRAW.FINALPAYMENT";
    public const string FinalPaymentV2 = "CREATE.DRAW.FINALPAYMENT.V2";
    public const string InvoiceDisbursementV2 = "CREATE.PAYNOW.DISBURSEMENT.V2";
    public const string InvoicePaymentV2 = "CREATE.PAYNOW.INVOICE_PAYMENT.V2";
    public const string InvoicePaymentCard = "CREATE.PAYNOW.INVOICE_PAYMENT.CARD";
    public const string FactoringDisbursement = "CREATE.FACTORING.DISBURSEMENT";
    public const string FactoringFinalPayment = "CREATE.FACTORING.FINAL_PAYMENT";
    public const string ManualPaymentPull = "CREATE.MANUAL.PAYMENT.PULL";
    public const string SubscriptionFeePayment = "CREATE.SUBSCRIPTIONFEE.PAYMENT";
}
