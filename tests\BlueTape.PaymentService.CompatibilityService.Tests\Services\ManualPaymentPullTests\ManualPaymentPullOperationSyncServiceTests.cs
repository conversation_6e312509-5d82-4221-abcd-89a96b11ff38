﻿using AutoMapper;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.ManualPaymentPull;
using BlueTape.PaymentService.CompatibilityService.Extensions;
using BlueTape.PaymentService.CompatibilityService.Services.ManualPaymentPull;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.LoanApplication;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Operation;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using NSubstitute;
using Xunit;

namespace BlueTape.PaymentService.CompatibilityService.Tests.Services.ManualPaymentPullTests
{
    public class ManualPaymentPullOperationSyncServiceTests
    {
        private readonly IOperationsRepository _operationsRepository;
        private readonly IManualPaymentPullTransactionSyncService _transactionSyncService;
        private readonly ILoanApplicationRepository _loanApplicationRepository;
        private readonly IMapper _mapper;
        private readonly IManualPaymentPullCompatibilityMapper _compatibilityMapper;
        private readonly ManualPaymentPullOperationSyncService _service;

        public ManualPaymentPullOperationSyncServiceTests()
        {
            _operationsRepository = Substitute.For<IOperationsRepository>();
            _transactionSyncService = Substitute.For<IManualPaymentPullTransactionSyncService>();
            _loanApplicationRepository = Substitute.For<ILoanApplicationRepository>();
            _compatibilityMapper = Substitute.For<IManualPaymentPullCompatibilityMapper>();

            _service = new ManualPaymentPullOperationSyncService(
                _operationsRepository,
                _transactionSyncService,
                _loanApplicationRepository,
                _compatibilityMapper
            );
        }

        [Fact]
        public async Task PerformOperation_ShouldGetLoanApplication_MapOperation_AndInsertIntoRepository()
        {
            var paymentRequest = CreateSamplePaymentRequest();
            var loanApplication = new LoanApplicationEntity { BlueTapeId = "bt-123" };
            var operation = new OperationEntity { BlueTapeId = "op-123" };
            var cancellationToken = CancellationToken.None;

            _loanApplicationRepository
                .GetByLmsId(paymentRequest.PaymentRequestDetails.DrawId.ToString(), cancellationToken)
                .Returns(loanApplication);

            _compatibilityMapper
                .MapFromPaymentRequestToOperation(paymentRequest, loanApplication.BlueTapeId)
                .Returns(operation);

            // Act
            await _service.PerformOperation(paymentRequest, cancellationToken);

            // Assert
            await _operationsRepository.Received(1).InsertMany(
                Arg.Is<List<OperationEntity>>(ops => ops.Count == 1 && ops[0] == operation),
                cancellationToken
            );

            await _transactionSyncService.Received(1).PerformTransactions(
                paymentRequest,
                operation,
                cancellationToken
            );
        }

        [Fact]
        public async Task SyncOperation_WhenExistingOperationFound_ShouldUpdateOperationAndSyncTransactions()
        {
            // Arrange
            var paymentRequest = CreateSamplePaymentRequest();
            var existingOperation = new OperationEntity { BlueTapeId = "op-123", OwnerId = "owner-123" };
            var cancellationToken = CancellationToken.None;

            _operationsRepository
                .GetByPaymentRequestId(paymentRequest.Id.ToString(), cancellationToken)
                .Returns(new List<OperationEntity> { existingOperation });

            // Act
            await _service.SyncOperation(paymentRequest, cancellationToken);

            // Assert
            await _operationsRepository.Received(1).Update(
                paymentRequest.Id.ToString(),
                Arg.Any<UpdateOperationEntity>(),
                cancellationToken
            );

            await _transactionSyncService.Received(1).SyncTransactions(
                paymentRequest,
                cancellationToken
            );

            // Verify PerformOperation wasn't called
            await _operationsRepository.DidNotReceive().InsertMany(
                Arg.Any<List<OperationEntity>>(),
                Arg.Any<CancellationToken>()
            );
        }

        [Fact]
        public async Task SyncOperation_WhenNoExistingOperationFound_ShouldCallPerformOperationAndSyncTransactions()
        {
            // Arrange
            var paymentRequest = CreateSamplePaymentRequest();
            var operation = new OperationEntity { BlueTapeId = "op-123" };
            var loanApplication = new LoanApplicationEntity { BlueTapeId = "bt-123" };
            var cancellationToken = CancellationToken.None;

            _operationsRepository
                .GetByPaymentRequestId(paymentRequest.Id.ToString(), cancellationToken)
                .Returns(new List<OperationEntity>());

            _loanApplicationRepository
                .GetByLmsId(paymentRequest.PaymentRequestDetails.DrawId.ToString(), cancellationToken)
                .Returns(loanApplication);

            _compatibilityMapper
                .MapFromPaymentRequestToOperation(paymentRequest, loanApplication.BlueTapeId)
                .Returns(operation);

            // Act
            await _service.SyncOperation(paymentRequest, cancellationToken);

            // Assert
            await _operationsRepository.Received(1).InsertMany(
                Arg.Any<List<OperationEntity>>(),
                cancellationToken
            );

            await _operationsRepository.Received(1).Update(
                paymentRequest.Id.ToString(),
                Arg.Any<UpdateOperationEntity>(),
                cancellationToken
            );

            await _transactionSyncService.Received(1).PerformTransactions(
                paymentRequest,
                Arg.Any<OperationEntity>(),
                cancellationToken
            );

            await _transactionSyncService.Received(1).SyncTransactions(
                paymentRequest,
                cancellationToken
            );
        }

        [Fact]
        public async Task SyncOperation_WhenExistingOperationHasEmptyOwnerId_ShouldFetchOwnerIdFromLoanApplication()
        {
            // Arrange
            var paymentRequest = CreateSamplePaymentRequest();
            var existingOperation = new OperationEntity { BlueTapeId = "op-123", OwnerId = string.Empty };
            var loanApplication = new LoanApplicationEntity { BlueTapeId = "bt-123" };
            var cancellationToken = CancellationToken.None;

            _operationsRepository
                .GetByPaymentRequestId(paymentRequest.Id.ToString(), cancellationToken)
                .Returns(new List<OperationEntity> { existingOperation });

            _loanApplicationRepository
                .GetByLmsId(paymentRequest.PaymentRequestDetails.DrawId.ToString(), cancellationToken)
                .Returns(loanApplication);

            // Act
            await _service.SyncOperation(paymentRequest, cancellationToken);

            // Assert
            await _loanApplicationRepository.Received(1).GetByLmsId(
                paymentRequest.PaymentRequestDetails.DrawId.ToString(),
                cancellationToken
            );

            await _operationsRepository.Received(1).Update(
                paymentRequest.Id.ToString(),
                Arg.Is<UpdateOperationEntity>(update => update.OwnerId == loanApplication.BlueTapeId),
                cancellationToken
            );
        }

        [Fact]
        public async Task SyncOperation_ShouldCreateUpdateOperationEntityWithCorrectData()
        {
            // Arrange
            var paymentRequest = CreateSamplePaymentRequest();
            var clearedTransaction = new PaymentTransactionEntity
            {
                Status = TransactionStatus.Cleared,
                UpdatedAt = DateTime.UtcNow
            };
            paymentRequest.Transactions.Add(clearedTransaction);

            var existingOperation = new OperationEntity { BlueTapeId = "op-123", OwnerId = "owner-123" };
            var cancellationToken = CancellationToken.None;

            _operationsRepository
                .GetByPaymentRequestId(paymentRequest.Id.ToString(), cancellationToken)
                .Returns(new List<OperationEntity> { existingOperation });

            // Capture the update operation entity for verification
            UpdateOperationEntity? capturedUpdate = null;
            _operationsRepository
                .When(x => x.Update(Arg.Any<string>(), Arg.Any<UpdateOperationEntity>(), Arg.Any<CancellationToken>()))
                .Do(info => capturedUpdate = info.Arg<UpdateOperationEntity>());

            // Act
            await _service.SyncOperation(paymentRequest, cancellationToken);

            // Assert
            Assert.NotNull(capturedUpdate);
            Assert.Equal(paymentRequest.Date.ToDateTime(new TimeOnly(), DateTimeKind.Utc), capturedUpdate.Date);
            Assert.Equal(paymentRequest.GetPaymentOperationStatus().ToString(), capturedUpdate.Status);
            Assert.Equal(PaymentMethod.Ach.ToString().ToLower(), capturedUpdate.PaymentMethod);
            Assert.Equal(paymentRequest.PayerId, capturedUpdate.PayerId);
            Assert.Equal(paymentRequest.PayeeId, capturedUpdate.PayeeId);
            Assert.Equal(paymentRequest.GetFirstTransactionDate(), capturedUpdate.FirstTransactionDate);
            Assert.Equal(paymentRequest.Id.ToString(), capturedUpdate.PaymentRequestId);
            Assert.Equal(clearedTransaction.UpdatedAt, capturedUpdate.PullResult);
            Assert.Equal(paymentRequest.PaymentRequestDetails.LMSPaymentId.ToString(), capturedUpdate.LmsPaymentId);
        }

        private PaymentRequestEntity CreateSamplePaymentRequest()
        {
            return new PaymentRequestEntity
            {
                Id = Guid.NewGuid(),
                PayerId = "payer-123",
                PayeeId = "payee-123",
                RequestType = PaymentRequestType.ManualPaymentPull,
                Amount = 1000,
                Date = DateOnly.FromDateTime(DateTime.Now),
                Status = PaymentRequestStatus.Requested,
                PaymentMethod = PaymentMethod.Ach,
                Transactions = new List<PaymentTransactionEntity>
                {
                    new PaymentTransactionEntity
                    {
                        Id = Guid.NewGuid(),
                        TransactionType = PaymentTransactionType.AchPull,
                        Amount = 1000,
                        CreatedAt = DateTime.UtcNow
                    }
                },
                PaymentRequestDetails = new PaymentRequestDetailsEntity
                {
                    DrawId = Guid.NewGuid(),
                    LMSPaymentId = Guid.NewGuid()
                }
            };
        }
    }
}