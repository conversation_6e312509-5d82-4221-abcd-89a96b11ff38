﻿using AutoMapper;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawDisbursement;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepaymentCard;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepaymentManual;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.FactoringDisbursement;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.FactoringFinalPayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.FinalPayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.InvoiceDisbursementV2;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.InvoicePayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.InvoicePaymentV2;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.ManualPaymentPull;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.SubscriptionFeePayment;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;
using TinyHelpers.Extensions;

namespace BlueTape.PaymentService.Application.Mappers;
public class MessageProfile : Profile
{
    public MessageProfile()
    {
        CreateMap<InvoicePaymentRequestMessage, CreatePaymentRequestModel>()
               .ForMember(dest => dest.FlowTemplateCode, opt => opt.MapFrom(src => src.FlowTemplateCode))
               .ForMember(dest => dest.PaymentMethod, opt => opt.MapFrom(src => Enum.Parse<PaymentMethod>(src.PaymentRequestDetails.PaymentMethod, true)))
               .ForMember(desc => desc.ConfirmationType, opt => opt.MapFrom(src => Enum.Parse<ConfirmationType>(src.PaymentRequestDetails.ConfirmationType, true)))
               .ForMember(dest => dest.PayerId, opt => opt.MapFrom(src => src.PaymentRequestDetails.CustomerDetails.Id))
               .ForMember(dest => dest.CustomerAccountId, opt => opt.MapFrom(src => src.PaymentRequestDetails.CustomerDetails.AccountId))
               .ForMember(dest => dest.PayeeId, opt => opt.MapFrom(src => src.PaymentRequestDetails.SellerDetails.CompanyId))
               .ForMember(dest => dest.SellerId, opt => opt.MapFrom(src => src.PaymentRequestDetails.SellerDetails.CompanyId))
               .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.PaymentRequestDetails.RequestedAmount))
               .ForMember(dest => dest.FeeAmount, opt => opt.MapFrom(src => src.PaymentRequestDetails.FeeDetails.Sum(x => x.Amount)))
               .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.PaymentRequestDetails.Currency))
               .ForMember(dest => dest.Date, opt => opt.MapFrom(src => DateOnly.FromDateTime(src.PaymentRequestDetails.Date)))
               .ForMember(dest => dest.MerchantAchDelayInBusinessDays, opt => opt.MapFrom(src => src.PaymentRequestDetails.SellerDetails.PaymentSettings.MerchantAchDelayDays))
               .ForMember(dest => dest.PaymentRequestPayables, opt => opt.MapFrom(src => src.PaymentRequestDetails.PayablesDetails))
               .ForMember(dest => dest.ProjectId, opt => opt.MapFrom(src => src.PaymentRequestDetails.ProjectDetails.Id))
               .ForMember(dest => dest.PaymentRequestFees, opt => opt.MapFrom(src => src.PaymentRequestDetails.FeeDetails))
               .ForMember(dest => dest.PaymentRequestDiscounts, opt => opt.MapFrom(src => src.PaymentRequestDetails.DiscountDetails));

        CreateMap<FactoringFinalPaymentRequestMessage, CreatePaymentRequestModel>()
            .ForMember(dest => dest.FlowTemplateCode, opt => opt.MapFrom(src => src.FlowTemplateCode))
            .ForMember(desc => desc.ConfirmationType, opt => opt.MapFrom(src => Enum.Parse<ConfirmationType>(src.PaymentRequestDetails.ConfirmationType, true)))
            .ForMember(dest => dest.PaymentMethod,
                opt => opt.MapFrom(src => Enum.Parse<PaymentMethod>(src.PaymentRequestDetails.PaymentMethod, true)))
            .ForMember(dest => dest.PayeeId,
                opt => opt.MapFrom(src => src.PaymentRequestDetails.SellerDetails.CompanyId))
            .ForMember(dest => dest.PayerId, opt => opt.MapFrom(src => src.PaymentRequestDetails.CustomerDetails.Id))
            .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.PaymentRequestDetails.RequestedAmount))
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.PaymentRequestDetails.Currency))
            .ForMember(dest => dest.Date,
                opt => opt.MapFrom(src => DateOnly.FromDateTime(src.PaymentRequestDetails.Date)))
            .ForMember(dest => dest.MerchantAchDelayInBusinessDays,
                opt => opt.MapFrom(src => src.PaymentRequestDetails.SellerDetails.PaymentSettings.MerchantAchDelayDays))
            .ForMember(dest => dest.PaymentRequestPayables,
                opt => opt.MapFrom(src => src.PaymentRequestDetails.PayablesDetails))
            .ForMember(dest => dest.SubjectType, opt => opt.MapFrom(src => SubjectType.Draw))
            .ForMember(dest => dest.DrawId, opt => opt.MapFrom(src => src.PaymentRequestDetails.DrawDetails.Id))
            .ForMember(dest => dest.RequestType, opt => opt.MapFrom(src => PaymentRequestType.FactoringFinalPayment))
            .ForMember(dest => dest.ExecuteAfter, opt => opt.MapFrom(src => src.PaymentRequestDetails.ExecuteAfter))
            .ForMember(dest => dest.PaymentRequestFees, opt => opt.MapFrom(src => src.PaymentRequestDetails.FeeDetails));
        CreateMap<InvoiceDisbursementV2RequestMessage, CreatePaymentRequestModel>()
            .ForMember(dest => dest.FlowTemplateCode, opt => opt.MapFrom(src => src.FlowTemplateCode))
            .ForMember(desc => desc.ConfirmationType,
                opt => opt.MapFrom(
                    src => Enum.Parse<ConfirmationType>(src.PaymentRequestDetails.ConfirmationType, true)))
            .ForMember(dest => dest.PaymentMethod,
                opt => opt.MapFrom(src => Enum.Parse<PaymentMethod>(src.PaymentRequestDetails.PaymentMethod, true)))
            .ForMember(dest => dest.PayerId, opt => opt.MapFrom(src => src.PaymentRequestDetails.CustomerDetails.Id))
            .ForMember(dest => dest.CustomerAccountId,
                opt => opt.MapFrom(src => src.PaymentRequestDetails.CustomerDetails.AccountId))
            .ForMember(dest => dest.PayeeId,
                opt => opt.MapFrom(src => src.PaymentRequestDetails.SellerDetails.CompanyId))
            .ForMember(dest => dest.SellerId,
                opt => opt.MapFrom(src => src.PaymentRequestDetails.SellerDetails.CompanyId))
            .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.PaymentRequestDetails.RequestedAmount))
            .ForMember(dest => dest.FeeAmount,
                opt => opt.MapFrom(src => src.PaymentRequestDetails.FeeDetails.Sum(x => x.Amount)))
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.PaymentRequestDetails.Currency))
            .ForMember(dest => dest.Date,
                opt => opt.MapFrom(src => DateOnly.FromDateTime(src.PaymentRequestDetails.Date)))
            .ForMember(dest => dest.MerchantAchDelayInBusinessDays,
                opt => opt.MapFrom(src => src.PaymentRequestDetails.SellerDetails.PaymentSettings.MerchantAchDelayDays))
            .ForMember(dest => dest.PaymentRequestPayables,
                opt => opt.MapFrom(src => src.PaymentRequestDetails.PayablesDetails))
            .ForMember(dest => dest.ProjectId, opt => opt.MapFrom(src => src.PaymentRequestDetails.ProjectDetails.Id))
            .ForMember(dest => dest.RequestType, opt => opt.MapFrom(src => PaymentRequestType.InvoiceDisbursementV2))
            .ForMember(dest => dest.PaymentRequestFees,
                opt => opt.MapFrom(src => src.PaymentRequestDetails.FeeDetails));
        CreateMap<InvoicePaymentV2RequestMessage, CreatePaymentRequestModel>()
                .ForMember(dest => dest.FlowTemplateCode, opt => opt.MapFrom(src => src.FlowTemplateCode))
                .ForMember(desc => desc.ConfirmationType, opt => opt.MapFrom(src => Enum.Parse<ConfirmationType>(src.PaymentRequestDetails.ConfirmationType, true)))
                .ForMember(dest => dest.PaymentMethod, opt => opt.MapFrom(src => Enum.Parse<PaymentMethod>(src.PaymentRequestDetails.PaymentMethod, true)))
                .ForMember(dest => dest.PayerId, opt => opt.MapFrom(src => src.PaymentRequestDetails.CustomerDetails.Id))
                .ForMember(dest => dest.CustomerAccountId, opt => opt.MapFrom(src => src.PaymentRequestDetails.CustomerDetails.AccountId))
                .ForMember(dest => dest.PayeeId, opt => opt.MapFrom(src => src.PaymentRequestDetails.SellerDetails.CompanyId))
                .ForMember(dest => dest.SellerId, opt => opt.MapFrom(src => src.PaymentRequestDetails.SellerDetails.CompanyId))
                .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.PaymentRequestDetails.RequestedAmount))
                .ForMember(dest => dest.FeeAmount, opt => opt.MapFrom(src => src.PaymentRequestDetails.FeeDetails.Sum(x => x.Amount)))
                .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.PaymentRequestDetails.Currency))
                .ForMember(dest => dest.Date, opt => opt.MapFrom(src => DateOnly.FromDateTime(src.PaymentRequestDetails.Date)))
                .ForMember(dest => dest.PaymentRequestPayables, opt => opt.MapFrom(src => src.PaymentRequestDetails.PayablesDetails))
                .ForMember(dest => dest.ProjectId, opt => opt.MapFrom(src => src.PaymentRequestDetails.ProjectDetails.Id))
                .ForMember(dest => dest.RequestType, opt => opt.MapFrom(src => PaymentRequestType.InvoicePaymentV2))
                .ForMember(dest => dest.ManualPaymentDetails, opt => opt.MapFrom(src => src.PaymentRequestDetails.ManualPaymentDetails))
                .ForMember(dest => dest.PaymentRequestFees, opt => opt.MapFrom(src => src.PaymentRequestDetails.FeeDetails))
                .ForMember(dest => dest.PaymentRequestDiscounts, opt => opt.MapFrom(src => src.PaymentRequestDetails.DiscountDetails));
        CreateMap<DrawRepaymentRequestMessage, CreatePaymentRequestModel>()
               .ForMember(dest => dest.FlowTemplateCode, opt => opt.MapFrom(src => src.FlowTemplateCode))
               .ForMember(desc => desc.ConfirmationType, opt => opt.MapFrom(src => Enum.Parse<ConfirmationType>(src.PaymentRequestDetails.ConfirmationType, true)))
               .ForMember(dest => dest.PaymentMethod, opt => opt.MapFrom(src => Enum.Parse<PaymentMethod>(src.PaymentRequestDetails.PaymentMethod, true)))
               .ForMember(dest => dest.PayerId, opt => opt.MapFrom(src => src.PaymentRequestDetails.CustomerDetails.Id))
               .ForMember(dest => dest.CustomerAccountId, opt => opt.MapFrom(src => src.PaymentRequestDetails.CustomerDetails.AccountId))
               .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.PaymentRequestDetails.RequestedAmount))
               .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.PaymentRequestDetails.Currency))
               .ForMember(dest => dest.Date, opt => opt.MapFrom(src => DateOnly.FromDateTime(src.PaymentRequestDetails.Date)))
               .ForMember(dest => dest.SubjectType, opt => opt.MapFrom(src => SubjectType.Draw))
               .ForMember(dest => dest.ManualPaymentDetails, opt => opt.MapFrom(src => src.PaymentRequestDetails.ManualPaymentDetails))
               .ForMember(dest => dest.DrawId, opt => opt.MapFrom(src => src.PaymentRequestDetails.DrawRepaymentDrawDetails.Id))
               .ForMember(
                    dest => dest.LmsPaymentId,
                    opt => opt.MapFrom(src =>
                        TryParseGuid(src.PaymentRequestDetails.CompatibilityDetails.LmsPaymentId)))
               .ForMember(dest => dest.RequestType, opt => opt.MapFrom(src => PaymentRequestType.DrawRepayment));
        CreateMap<DrawRepaymentCardRequestMessage, CreatePaymentRequestModel>()
            .ForMember(dest => dest.FlowTemplateCode, opt => opt.MapFrom(src => src.FlowTemplateCode))
            .ForMember(desc => desc.ConfirmationType, opt => opt.MapFrom(src => Enum.Parse<ConfirmationType>(src.PaymentRequestDetails.ConfirmationType, true)))
            .ForMember(dest => dest.PaymentMethod, opt => opt.MapFrom(src => Enum.Parse<PaymentMethod>(src.PaymentRequestDetails.PaymentMethod, true)))
            .ForMember(dest => dest.PayerId, opt => opt.MapFrom(src => src.PaymentRequestDetails.CustomerDetails.Id))
            .ForMember(dest => dest.CustomerAccountId, opt => opt.MapFrom(src => src.PaymentRequestDetails.CustomerDetails.AccountId))
            .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.PaymentRequestDetails.RequestedAmount))
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.PaymentRequestDetails.Currency))
            .ForMember(dest => dest.Date, opt => opt.MapFrom(src => DateOnly.FromDateTime(src.PaymentRequestDetails.Date)))
            .ForMember(dest => dest.SubjectType, opt => opt.MapFrom(src => SubjectType.Draw))
            .ForMember(dest => dest.DrawId, opt => opt.MapFrom(src => src.PaymentRequestDetails.DrawRepaymentDrawDetails.Id))
            .ForMember(dest => dest.LmsPaymentId, opt => opt.MapFrom(src => src.PaymentRequestDetails.CompatibilityDetails.LmsPaymentId))
            .ForMember(dest => dest.ParentReferenceNumber, opt => opt.MapFrom(src => src.PaymentRequestDetails.CardPaymentDetails.TransactionId))
            .ForMember(dest => dest.RequestType, opt => opt.MapFrom(src => PaymentRequestType.DrawRepaymentCard));
        CreateMap<DrawRepaymentManualRequestMessage, CreatePaymentRequestModel>()
            .ForMember(dest => dest.FlowTemplateCode, opt => opt.MapFrom(src => src.FlowTemplateCode))
            .ForMember(dest => dest.PaymentMethod, opt => opt.MapFrom(src => Enum.Parse<PaymentMethod>(src.PaymentRequestDetails.PaymentMethod, true)))
            .ForMember(dest => dest.PayerId, opt => opt.MapFrom(src => src.PaymentRequestDetails.CustomerDetails.Id))
            .ForMember(dest => dest.CustomerAccountId, opt => opt.MapFrom(src => src.PaymentRequestDetails.CustomerDetails.AccountId))
            .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.PaymentRequestDetails.RequestedAmount))
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.PaymentRequestDetails.Currency))
            .ForMember(dest => dest.Date, opt => opt.MapFrom(src => DateOnly.FromDateTime(src.PaymentRequestDetails.Date)))
            .ForMember(dest => dest.SubjectType, opt => opt.MapFrom(src => SubjectType.Draw))
            .ForMember(dest => dest.DrawId, opt => opt.MapFrom(src => src.PaymentRequestDetails.DrawDetails.Id))
            .ForMember(dest => dest.ManualPaymentDetails, opt => opt.MapFrom(src => src.PaymentRequestDetails.ManualPaymentDetails))
            .ForMember(dest => dest.RequestType, opt => opt.MapFrom(src => PaymentRequestType.DrawRepaymentManual));
        CreateMap<ManualPaymentPullRequestMessage, CreatePaymentRequestModel>()
            .ForMember(dest => dest.FlowTemplateCode, opt => opt.MapFrom(src => src.FlowTemplateCode))
            .ForMember(dest => dest.PaymentMethod, opt => opt.MapFrom(src => Enum.Parse<PaymentMethod>(src.PaymentRequestDetails.PaymentMethod, true)))
            .ForMember(dest => dest.PayerId, opt => opt.MapFrom(src => src.PaymentRequestDetails.CustomerDetails.Id))
            .ForMember(dest => dest.CustomerAccountId, opt => opt.MapFrom(src => src.PaymentRequestDetails.CustomerDetails.AccountId))
            .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.PaymentRequestDetails.RequestedAmount))
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.PaymentRequestDetails.Currency))
            .ForMember(dest => dest.Date, opt => opt.MapFrom(src => DateOnly.FromDateTime(src.PaymentRequestDetails.Date)))
            .ForMember(dest => dest.SubjectType, opt => opt.MapFrom(src => SubjectType.Draw))
            .ForMember(dest => dest.ManualPaymentDetails, opt => opt.MapFrom(src => src.PaymentRequestDetails.ManualPaymentDetails))
            .ForMember(dest => dest.RequestType, opt => opt.MapFrom(src => PaymentRequestType.ManualPaymentPull));
        CreateMap<FinalPaymentRequestMessage, CreatePaymentRequestModel>()
               .ForMember(dest => dest.FlowTemplateCode, opt => opt.MapFrom(src => src.FlowTemplateCode))
               .ForMember(desc => desc.ConfirmationType, opt => opt.MapFrom(src => Enum.Parse<ConfirmationType>(src.PaymentRequestDetails.ConfirmationType, true)))
               .ForMember(dest => dest.PaymentMethod, opt => opt.MapFrom(src => Enum.Parse<PaymentMethod>(src.PaymentRequestDetails.PaymentMethod, true)))
               .ForMember(dest => dest.PayeeId, opt => opt.MapFrom(src => src.PaymentRequestDetails.SellerDetails.CompanyId))
               .ForMember(dest => dest.PayerId, opt => opt.MapFrom(src => src.PaymentRequestDetails.CustomerDetails.Id))
               .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.PaymentRequestDetails.RequestedAmount))
               .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.PaymentRequestDetails.Currency))
               .ForMember(dest => dest.Date, opt => opt.MapFrom(src => DateOnly.FromDateTime(src.PaymentRequestDetails.Date)))
               .ForMember(dest => dest.MerchantAchDelayInBusinessDays, opt => opt.MapFrom(src => src.PaymentRequestDetails.SellerDetails.PaymentSettings.MerchantAchDelayDays))
               .ForMember(dest => dest.PaymentRequestPayables, opt => opt.MapFrom(src => src.PaymentRequestDetails.PayablesDetails))
               .ForMember(dest => dest.SubjectType, opt => opt.MapFrom(src => SubjectType.Draw))
               .ForMember(dest => dest.DrawId, opt => opt.MapFrom(src => src.PaymentRequestDetails.DrawDetails.Id))
               .ForMember(dest => dest.RequestType, opt => opt.MapFrom(src =>
                   src.FlowTemplateCode == DomainConstants.FinalPayment ? PaymentRequestType.FinalPayment : PaymentRequestType.FinalPaymentV2))
               .ForMember(dest => dest.ExecuteAfter, opt => opt.MapFrom(src => src.PaymentRequestDetails.ExecuteAfter))
               .ForMember(dest => dest.PaymentRequestFees, opt => opt.MapFrom(src => src.PaymentRequestDetails.FeeDetails));
        CreateMap<FactoringDisbursementRequestMessage, CreatePaymentRequestModel>()
                .ForMember(dest => dest.FlowTemplateCode, opt => opt.MapFrom(src => src.FlowTemplateCode))
                .ForMember(desc => desc.ConfirmationType, opt => opt.MapFrom(src => Enum.Parse<ConfirmationType>(src.PaymentRequestDetails.ConfirmationType, true)))
                .ForMember(dest => dest.PaymentMethod, opt => opt.MapFrom(src => Enum.Parse<PaymentMethod>(src.PaymentRequestDetails.PaymentMethod, true)))
                .ForMember(dest => dest.PayeeId, opt => opt.MapFrom(src => src.PaymentRequestDetails.SellerDetails.CompanyId))
                .ForMember(dest => dest.PayerId, opt => opt.MapFrom(src => src.PaymentRequestDetails.CustomerDetails.Id))
                .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.PaymentRequestDetails.RequestedAmount))
                .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.PaymentRequestDetails.Currency))
                .ForMember(dest => dest.Date, opt => opt.MapFrom(src => DateOnly.FromDateTime(src.PaymentRequestDetails.Date)))
                .ForMember(dest => dest.PaymentRequestPayables, opt => opt.MapFrom(src => src.PaymentRequestDetails.PayablesDetails))
                .ForMember(dest => dest.SubjectType, opt => opt.MapFrom(src => SubjectType.Draw))
                .ForMember(dest => dest.DrawId, opt => opt.MapFrom(src => src.PaymentRequestDetails.DrawDetails.Id))
                .ForMember(dest => dest.RequestType, opt => opt.MapFrom(src => PaymentRequestType.FactoringDisbursement))
                .ForMember(dest => dest.ExecuteAfter, opt => opt.MapFrom(src => src.PaymentRequestDetails.ExecuteAfter))
                .ForMember(dest => dest.PaymentRequestFees, opt => opt.MapFrom(src => src.PaymentRequestDetails.FeeDetails));
        CreateMap<SubscriptionFeePaymentRequestMessage, CreatePaymentRequestModel>()
            .ForMember(dest => dest.FlowTemplateCode, opt => opt.MapFrom(src => src.FlowTemplateCode))
            .ForMember(dest => dest.PaymentMethod,
                opt => opt.MapFrom(src => Enum.Parse<PaymentMethod>(src.PaymentRequestDetails.PaymentMethod, true)))
            .ForMember(dest => dest.PayerId, opt => opt.MapFrom(src => src.PaymentRequestDetails.CustomerDetails.Id))
            .ForMember(dest => dest.CustomerAccountId,
                opt => opt.MapFrom(src => src.PaymentRequestDetails.CustomerDetails.AccountId))
            .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.PaymentRequestDetails.RequestedAmount))
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.PaymentRequestDetails.Currency))
            .ForMember(dest => dest.Date,
                opt => opt.MapFrom(src => DateOnly.FromDateTime(src.PaymentRequestDetails.Date)))
            .ForMember(dest => dest.SubjectType, opt => opt.MapFrom(src => SubjectType.Draw))
            .ForMember(dest => dest.RequestType, opt => opt.MapFrom(src => PaymentRequestType.SubscriptionFeePayment))
            .ForMember(dest => dest.ConfirmationType,
                opt => opt.MapFrom(src => src.PaymentRequestDetails.ConfirmationType))
            .ForMember(dest => dest.AdditionalDetails,
                opt => opt.MapFrom(src => src.PaymentRequestDetails.AdditionalDetails));

        CreateMap<DrawDisbursementRequestMessage, CreatePaymentRequestModel>()
            .ForMember(dest => dest.FlowTemplateCode, opt => opt.MapFrom(src => src.FlowTemplateCode))
            .ForMember(desc => desc.ConfirmationType, opt => opt.MapFrom(src => Enum.Parse<ConfirmationType>(src.PaymentRequestDetails.ConfirmationType, true)))
            .ForMember(dest => dest.PaymentMethod, opt => opt.MapFrom(src => Enum.Parse<PaymentMethod>(src.PaymentRequestDetails.PaymentMethod, true)))
            .ForMember(dest => dest.PayeeId, opt => opt.MapFrom(src => src.PaymentRequestDetails.SellerDetails.CompanyId))
            .ForMember(dest => dest.PayerId, opt => opt.MapFrom(src => src.PaymentRequestDetails.CustomerDetails.Id))
            .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.PaymentRequestDetails.RequestedAmount))
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.PaymentRequestDetails.Currency))
            .ForMember(dest => dest.Date, opt => opt.MapFrom(src => DateOnly.FromDateTime(src.PaymentRequestDetails.Date)))
            .ForMember(dest => dest.PaymentRequestPayables, opt => opt.MapFrom(src => src.PaymentRequestDetails.PayablesDetails))
            .ForMember(dest => dest.SubjectType, opt => opt.MapFrom(src => SubjectType.Draw))
            .ForMember(dest => dest.DrawId, opt => opt.MapFrom(src => src.PaymentRequestDetails.DrawDetails.Id))
            .ForMember(dest => dest.RequestType, opt => opt.MapFrom(src => PaymentRequestType.DrawDisbursement))
            .ForMember(dest => dest.ExecuteAfter, opt => opt.MapFrom(src => src.PaymentRequestDetails.ExecuteAfter))
            .ForMember(dest => dest.PaymentRequestFees, opt => opt.MapFrom(src => src.PaymentRequestDetails.FeeDetails));
        CreateMap<FeeDetails, CreatePaymentRequestFeeModel>()
            .ForMember(dest => dest.Type, opt => opt.MapFrom(src => Enum.Parse<FeeType>(src.Type.FirstCharToUpper())));
        CreateMap<DiscountDetails, CreatePaymentRequestDiscountModel>()
            .ForMember(dest => dest.Type, opt => opt.MapFrom(src => Enum.Parse<FeeType>(src.Type.FirstCharToUpper())));
        CreateMap<PayablesDetail, CreatePaymentRequestPayableModel>()
            .ForMember(dest => dest.PayableType, opt => opt.MapFrom(src => TryParseEnum<PayableType>(src.PayableType) ?? PayableType.Invoice));
        CreateMap<ManualPaymentDetails, CreateManualPaymentDetails>();
        CreateMap<AdditionalDetails, CreateAdditionalDetails>();
    }

    private static Guid? TryParseGuid(string? value) => Guid.TryParse(value, out var g) ? g : (Guid?)null;

    private static T? TryParseEnum<T>(string? value) where T : struct, Enum
        => !string.IsNullOrWhiteSpace(value) && Enum.TryParse<T>(value, true, out var result) ? result : default;
}