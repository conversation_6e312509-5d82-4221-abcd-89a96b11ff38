﻿using BlueTape.PaymentService.CompatibilityService.Abstractions.Service;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.DrawDisbursement;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.DrawRepaymentCard;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.DrawRepaymentManual;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.DrawRepayments;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.FactoringDisbursement;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.FactoringFinalPayment;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.FinalPayment;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.InvoicePayment;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.InvoicePaymentV2;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.ManualPaymentPull;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Strategies;
using BlueTape.PaymentService.CompatibilityService.Extensions;
using BlueTape.PaymentService.CompatibilityService.MappingProfiles;
using BlueTape.PaymentService.CompatibilityService.Services;
using BlueTape.PaymentService.CompatibilityService.Services.DrawDisbursement;
using BlueTape.PaymentService.CompatibilityService.Services.DrawRepaymentCard;
using BlueTape.PaymentService.CompatibilityService.Services.DrawRepaymentManual;
using BlueTape.PaymentService.CompatibilityService.Services.DrawRepayments;
using BlueTape.PaymentService.CompatibilityService.Services.FactoringDisbursement;
using BlueTape.PaymentService.CompatibilityService.Services.FactoringFinalPayment;
using BlueTape.PaymentService.CompatibilityService.Services.FinalPayment;
using BlueTape.PaymentService.CompatibilityService.Services.InvoicePayment;
using BlueTape.PaymentService.CompatibilityService.Services.InvoicePaymentV2;
using BlueTape.PaymentService.CompatibilityService.Services.ManualPaymentPull;
using BlueTape.PaymentService.CompatibilityService.Strategies;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;

namespace BlueTape.PaymentService.CompatibilityService.DI;

public static class DependencyInjection
{
    public static void UseCompatibilityService(this IServiceCollection services)
    {
        services.AddScoped<IPaymentCompatibilityService, PaymentCompatibilityService>();

        AddPayNowServices(services);
        AddInvoicePaymentV2Services(services);
        AddDrawRepaymentsServices(services);
        AddDrawRepaymentCardServices(services);
        AddDrawRepaymentManualServices(services);
        AddCompatibilityStrategies(services);
        AddFinalPaymentServices(services);
        AddFactoringFinalPaymentServices(services);
        AddFactoringDisbursementServices(services);
        AddDrawDisbursementServices(services);
        AddManualPaymentPullServices(services);

        services.AddAutoMapper(typeof(SyncModelsMappingProfiles).GetTypeInfo().Assembly);
        services.AddScoped<IOperationPaymentSyncHelper, OperationPaymentSyncHelper>();
        services.AddScoped<ILoanCompatibilityService, LoanCompatibilityService>();
    }

    public static void AddPayNowServices(IServiceCollection services)
    {
        services.AddScoped<IInvoicePaymentCompatibilityMapper, InvoicePaymentCompatibilityMapper>();
        services.AddScoped<IInvoicePaymentOperationsSyncService, InvoicePaymentOperationsSyncService>();
        services.AddScoped<IInvoicePaymentTransactionsSyncService, InvoicePaymentTransactionsSyncService>();
    }

    public static void AddInvoicePaymentV2Services(IServiceCollection services)
    {
        services.AddScoped<IInvoicePaymentV2CompatibilityMapper, InvoicePaymentV2CompatibilityMapper>();
        services.AddScoped<IInvoicePaymentV2OperationsSyncService, InvoicePaymentV2OperationsSyncService>();
        services.AddScoped<IInvoicePaymentV2TransactionsSyncService, InvoicePaymentV2TransactionsSyncService>();
    }

    public static void AddDrawDisbursementServices(IServiceCollection services)
    {
        services.AddScoped<IDrawDisbursementOperationSyncService, DrawDisbursementOperationSyncService>();
        services.AddScoped<IDrawDisbursementTransactionSyncService, DrawDisbursementTransactionSyncService>();
        services.AddScoped<IDrawDisbursementCompatibilityMapper, DrawDisbursementCompatibilityMapper>();
    }

    public static void AddDrawRepaymentsServices(IServiceCollection services)
    {
        services.AddScoped<IDrawRepaymentsOperationSyncService, DrawRepaymentsOperationSyncService>();
        services.AddScoped<IDrawRepaymentsTransactionSyncService, DrawRepaymentsTransactionSyncService>();
        services.AddScoped<IDrawRepaymentsCompatibilityMapper, DrawRepaymentsCompatibilityMapper>();
    }

    public static void AddDrawRepaymentCardServices(IServiceCollection services)
    {
        services.AddScoped<IDrawRepaymentCardOperationSyncService, DrawRepaymentCardOperationSyncService>();
        services.AddScoped<IDrawRepaymentCardTransactionSyncService, DrawRepaymentCardTransactionSyncService>();
        services.AddScoped<IDrawRepaymentCardCompatibilityMapper, DrawRepaymentCardCompatibilityMapper>();
    }

    public static void AddDrawRepaymentManualServices(IServiceCollection services)
    {
        services.AddScoped<IDrawRepaymentManualOperationSyncService, DrawRepaymentManualOperationSyncService>();
        services.AddScoped<IDrawRepaymentManualTransactionSyncService, DrawRepaymentManualTransactionSyncService>();
        services.AddScoped<IDrawRepaymentManualCompatibilityMapper, DrawRepaymentManualCompatibilityMapper>();
    }

    public static void AddFinalPaymentServices(IServiceCollection services)
    {
        services.AddScoped<IFinalPaymentCompatibilityMapper, FinalPaymentCompatibilityMapper>();
        services.AddScoped<IFinalPaymentOperationSyncService, FinalPaymentOperationSyncService>();
        services.AddScoped<IFinalPaymentTransactionSyncService, FinalPaymentTransactionSyncService>();
    }

    public static void AddFactoringFinalPaymentServices(IServiceCollection services)
    {
        services.AddScoped<IFactoringFinalPaymentCompatibilityMapper, FactoringFinalPaymentCompatibilityMapper>();
        services.AddScoped<IFactoringFinalPaymentOperationSyncService, FactoringFinalPaymentOperationSyncService>();
        services.AddScoped<IFactoringFinalPaymentTransactionSyncService, FactoringFinalPaymentTransactionSyncService>();
    }

    public static void AddFactoringDisbursementServices(IServiceCollection services)
    {
        services.AddScoped<IFactoringDisbursementCompatibilityMapper, FactoringDisbursementCompatibilityMapper>();
        services.AddScoped<IFactoringDisbursementOperationSyncService, FactoringDisbursementOperationSyncService>();
        services.AddScoped<IFactoringDisbursementTransactionSyncService, FactoringDisbursementTransactionSyncService>();
    }

    public static void AddManualPaymentPullServices(IServiceCollection services)
    {
        services.AddScoped<IManualPaymentPullOperationSyncService, ManualPaymentPullOperationSyncService>();
        services.AddScoped<IManualPaymentPullTransactionSyncService, ManualPaymentPullTransactionSyncService>();
        services.AddScoped<IManualPaymentPullCompatibilityMapper, ManualPaymentPullCompatibilityMapper>();
    }

    public static void AddCompatibilityStrategies(IServiceCollection services)
    {
        services.AddScoped<ICompatibilityStrategy, InvoicePaymentCompatibilityStrategy>();
        services.AddScoped<ICompatibilityStrategy, InvoicePaymentV2CompatibilityStrategy>();
        services.AddScoped<ICompatibilityStrategy, InvoicePaymentCardCompatibilityStrategy>();
        services.AddScoped<ICompatibilityStrategy, DrawRepaymentsCompatibilityStrategy>();
        services.AddScoped<ICompatibilityStrategy, FinalPaymentCompatibilityStrategy>();
        services.AddScoped<ICompatibilityStrategy, FactoringFinalPaymentCompatibilityStrategy>();
        services.AddScoped<ICompatibilityStrategy, FactoringDisbursementCompatibilityStrategy>();
        services.AddScoped<ICompatibilityStrategy, DrawDisbursementCompatibilityStrategy>();
        services.AddScoped<ICompatibilityStrategy, SubscriptionFeePaymentCompatibilityStrategy>();
        services.AddScoped<ICompatibilityStrategy, DrawRepaymentsCardCompatibilityStrategy>();
        services.AddScoped<ICompatibilityStrategy, DrawRepaymentsManualCompatibilityStrategy>();
        services.AddScoped<ICompatibilityStrategy, ManualPaymentPullCompatibilityStrategy>();
    }
}
