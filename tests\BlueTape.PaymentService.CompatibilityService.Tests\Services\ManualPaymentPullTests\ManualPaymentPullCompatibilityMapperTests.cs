﻿using BlueTape.PaymentService.CompatibilityService.Models;
using BlueTape.PaymentService.CompatibilityService.Services.ManualPaymentPull;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Operation;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Enums.Legacy;
using BlueTape.PaymentService.Domain.Extensions;
using MongoDB.Bson;
using NSubstitute;
using System.Reflection;
using Xunit;

namespace BlueTape.PaymentService.CompatibilityService.Tests.Services.ManualPaymentPullTests
{
    public class ManualPaymentPullCompatibilityMapperTests
    {
        private readonly IBankAccountRepository _bankAccountRepository;
        private readonly ManualPaymentPullCompatibilityMapper _mapper;

        public ManualPaymentPullCompatibilityMapperTests()
        {
            _bankAccountRepository = Substitute.For<IBankAccountRepository>();
            _mapper = new ManualPaymentPullCompatibilityMapper(_bankAccountRepository);
        }

        [Fact]
        public void MapFromPaymentRequestToOperation_ShouldMapCorrectly()
        {
            var paymentRequest = CreateSamplePaymentRequest();
            var ownerId = "owner123";

            var result = _mapper.MapFromPaymentRequestToOperation(paymentRequest, ownerId);

            Assert.NotNull(result);
            Assert.Equal(ownerId, result.OwnerId);
            Assert.Equal(OperationStatus.PLACED.ToString(), result.Status);
            Assert.Equal(paymentRequest.RequestType.MapFromPaymentRequestTypeToOperationType(), result.Type);
            Assert.Equal(paymentRequest.Amount, result.Amount);
            Assert.Equal(paymentRequest.Date.ToDateTime(new TimeOnly(), DateTimeKind.Utc), result.Date);
            Assert.NotNull(result.Metadata);
            Assert.Equal(paymentRequest.PayerId, result.Metadata.PayerId);
            Assert.Equal(paymentRequest.PaymentMethod.ToString().ToLower(), result.Metadata.PaymentMethod);
            Assert.Equal(paymentRequest.Id.ToString(), result.PaymentRequestId);
            Assert.Equal(DomainConstants.PaymentService, result.CreatedBy);
            Assert.Equal(PaymentProvider.Aion.ToString(), result.PaymentProvider);
        }

        [Fact]
        public async Task MapFromPaymentTransactionsToLegacyTransactions_ShouldMapCorrectly()
        {
            var paymentRequest = CreateSamplePaymentRequest();
            var operation = new OperationEntity
            {
                BlueTapeId = ObjectId.GenerateNewId().ToString()!
            };

            var result = await _mapper.MapFromPaymentTransactionsToLegacyTransactions(paymentRequest, operation, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(2, result.Count);

            foreach (var transaction in result)
            {
                Assert.Equal(operation.BlueTapeId, transaction.OperationId);
                Assert.Equal(PaymentTransactionType.AchPull.ToString().ToLower(), transaction.Type);
                Assert.Equal(paymentRequest.PayerId, transaction.PayerId);
                Assert.Equal(PaymentMethod.Ach.ToString().ToLower(), transaction.PaymentMethod);
                Assert.Equal(OperationStatus.PLACED.ToString(), transaction.Status);
                Assert.Equal(DomainConstants.PaymentService, transaction.CreatedBy);
                Assert.Equal(PaymentProvider.Aion.ToString(), transaction.Provider);
                Assert.NotNull(transaction.Metadata);
            }
        }

        [Fact]
        public async Task MapSyncModelToUpdateTransactionEntity_ShouldMapCorrectly()
        {
            var syncModel = new SyncTransactionModel
            {
                Date = DateTime.UtcNow,
                Status = TransactionStatus.Cleared,
                ReceiverAccountId = "account123",
                ReferenceNumber = "ref123",
                TransactionNumber = "trans123",
                ExecutedAt = DateTime.UtcNow,
                StatusCode = "200",
                PaymentTransactionId = "payment123",
                Provider = "Aion",
                PublicTransactionNumber = "public123",
                StatusReason = "Success",
                StatusDataTransactionStatus = StatusDataTransactionStatus.PENDING
            };

            var result = await _mapper.MapSyncModelToUpdateTransactionEntity(syncModel, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(syncModel.Date, result.Date);
            Assert.Equal("SUCCESS", result.Status);
            Assert.Equal(syncModel.ReceiverAccountId, result.StatusDataAccountId);
            Assert.Equal(syncModel.ReferenceNumber, result.StatusDataTransactionNumber);
            Assert.Equal(syncModel.TransactionNumber, result.OriginalReference);
            Assert.Equal(syncModel.ReferenceNumber, result.MetadataTransactionNumber);
            Assert.Equal(syncModel.ExecutedAt, result.ApiDateTime);
            Assert.Equal(DomainConstants.PaymentService, result.UpdatedBy);
            Assert.Equal(syncModel.StatusCode, result.StatusCode);
            Assert.Equal(syncModel.PaymentTransactionId, result.PaymentTransactionId);
            Assert.Equal(syncModel.Provider, result.Provider);
            Assert.Equal(syncModel.PublicTransactionNumber, result.PublicTransactionNumber);
            Assert.Equal(syncModel.StatusReason, result.StatusReason);
            Assert.Equal(syncModel.StatusDataTransactionStatus, result.StatusDataTransactionStatus);
        }

        [Fact]
        public async Task GetTransactionMetadata_ShouldCreateCorrectMetadata()
        {
            var transaction = CreateSampleTransaction();
            var amount = 100.50m;

            MethodInfo methodInfo = typeof(ManualPaymentPullCompatibilityMapper).GetMethod(
                "GetTransactionMetadata",
                BindingFlags.NonPublic | BindingFlags.Instance);

            var result = await (Task<BlueTape.PaymentService.DataAccess.Mongo.Entities.Transaction.TransactionMetadataEntity>)
                methodInfo.Invoke(_mapper, new object[] { transaction, amount, CancellationToken.None });

            Assert.NotNull(result);
            Assert.Equal(LegacyTransactionType.PULL.ToString(), result.TransactionType);
            Assert.Equal(string.Empty, result.Payload);
            Assert.NotNull(result.StatusData);
            Assert.Equal(10050, result.StatusData.TransactionAmountCents);
            Assert.NotNull(result.StatusData.Account);
            Assert.Equal(transaction.ReceiverAccountId, result.StatusData.Account.AccountId);
            Assert.Equal(0, result.StatusData.Account.BalanceCents);
            Assert.Equal(0, result.StatusData.Account.HoldBalanceCents);
            Assert.Equal(LegacyPaymentFlowConstants.DrawRepaymentTransactionAccountStatus, result.StatusData.Account.Status);
            Assert.Equal(transaction.ReferenceNumber, result.StatusData.TransactionNumber);
            Assert.Equal(transaction.PublicTransactionNumber, result.PublicTransactionNumber);
        }

        private PaymentRequestEntity CreateSamplePaymentRequest()
        {
            var transactions = new List<PaymentTransactionEntity>
            {
                CreateSampleTransaction(),
                CreateSampleTransaction()
            };

            return new PaymentRequestEntity
            {
                Id = Guid.NewGuid(),
                PayerId = "payer123",
                RequestType = PaymentRequestType.ManualPaymentPull,
                Amount = 1000,
                Date = DateOnly.FromDateTime(DateTime.UtcNow),
                PaymentMethod = PaymentMethod.Ach,
                Status = PaymentRequestStatus.Requested,
                Transactions = transactions,
                PaymentRequestDetails = new PaymentRequestDetailsEntity
                {
                    Metadata = "{}"
                }
            };
        }

        private PaymentTransactionEntity CreateSampleTransaction()
        {
            return new PaymentTransactionEntity
            {
                Id = Guid.NewGuid(),
                TransactionType = PaymentTransactionType.AchPull,
                Amount = 500,
                Currency = "USD",
                CreatedAt = DateTime.UtcNow.AddDays(-1),
                Date = DateOnly.FromDateTime(DateTime.UtcNow),
                ReceiverAccountId = "account123",
                ReferenceNumber = "ref" + Guid.NewGuid().ToString().Substring(0, 8),
                Status = TransactionStatus.Placed,
                TransactionNumber = "trans" + Guid.NewGuid().ToString().Substring(0, 8),
                PublicTransactionNumber = "public" + Guid.NewGuid().ToString().Substring(0, 8),
                ExecutedAt = DateTime.UtcNow
            };
        }
    }
}
