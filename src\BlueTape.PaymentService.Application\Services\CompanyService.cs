﻿using BlueTape.CompanyClient.Abstractions;
using BlueTape.CompanyService.Companies;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Models.Company;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using Microsoft.Extensions.Logging;
using Polly;
using Polly.Retry;
using TinyHelpers.Extensions;

namespace BlueTape.PaymentService.Application.Services;
public class CompanyService(
    ICustomerRepository customerRepository,
    IUserRepository userRepository,
    IUserRoleRepository userRoleRepository,
    ICompanyHttpClient companyHttpClient,
    IForbiddenCompanyRepository forbiddenCompanyRepository,
    ILogger<CompanyService> logger) : ICompanyService
{

    public async Task<IEnumerable<CompanyModel>?> GetCompaniesByCustomerId(string customerId, CancellationToken ct)
    {
        var customer = await customerRepository.GetByIdAsync(customerId, ct);
        if (customer is null)
            return null;

        var logins = new List<string>();

        if (!string.IsNullOrEmpty(customer.Phone))
            logins.Add(customer.Phone);

        if (!string.IsNullOrEmpty(customer.EmailAddress))
            logins.Add(customer.EmailAddress);

        if (logins.IsNullOrEmpty())
            return null;

        var users = await userRepository.GetByLoginInformationAsync(logins, ct);

        if (users.IsNullOrEmpty())
            return null;

        var roles = await userRoleRepository.GetByExternalIds(users.Select(x => x.ExternalId), ct);

        var companyIds = roles.Select(x => x.CompanyId).Distinct().ToArray();

        return await companyHttpClient.GetCompaniesByIdsAsync(companyIds, ct);
    }

    public async Task<List<CompanyModel>> GetForbiddenCompanies(CancellationToken ct)
    {
        var forbiddenCompanies = await forbiddenCompanyRepository.Get(ct);

        var companies = await companyHttpClient.GetCompaniesByIdsAsync(forbiddenCompanies.Select(x => x.CompanyId).ToArray(), ct);

        return companies;
    }

    public async Task<CompanyModel?> AddCompanyToForbiddenList(ForbidCompanyRequest request, string createdBy, CancellationToken ct)
    {
        var company = await companyHttpClient.GetCompanyByIdAsync(request.CompanyId, ct);

        if (company is null)
            return null;

        var companies = await forbiddenCompanyRepository.Get(x => x.CompanyId == request.CompanyId, ct);
        if (!companies.IsNullOrEmpty())
            return null;
        var entity = new Domain.Entities.ForbiddenCompanyEntity
        { CompanyId = company.Id, PauseReason = request.Reason, CreatedBy = createdBy };

        await forbiddenCompanyRepository.Add(entity, ct);

        logger.LogInformation("The company with ID {id} has been added to the forbidden list for payments.", company.Id);

        return company;
    }

    public async Task RemoveCompanyFromForbiddenList(string companyId, CancellationToken ct)
    {
        var companies = await forbiddenCompanyRepository.Get(x => x.CompanyId == companyId, ct);

        foreach (var company in companies)
        {
            await forbiddenCompanyRepository.Delete(company, ct);
        }
    }

    public async Task<bool> AreCompaniesPaymentsForbidden(IEnumerable<string> companyIds, CancellationToken ct)
    {
        var companies = await forbiddenCompanyRepository.Get(x => companyIds.Contains(x.CompanyId), ct);
        return companies.Any();
    }

    public async Task<List<CompanyModel>> SearchCompaniesByName(string searchTerm, CancellationToken ct)
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
            return new List<CompanyModel>();

        try
        {
            // TODO: Implement company search when the ICompanyHttpClient interface supports search functionality
            // For now, return empty list as the search method is not available in the current interface
            // The endpoint should be: /companies?Search={searchTerm}&PageNumber=1&PageSize=1000

            logger.LogWarning("Company search by name is not yet implemented. SearchTerm: {SearchTerm}", searchTerm);
            return new List<CompanyModel>();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to search companies by name: {SearchTerm}", searchTerm);
            return new List<CompanyModel>();
        }
    }
}
