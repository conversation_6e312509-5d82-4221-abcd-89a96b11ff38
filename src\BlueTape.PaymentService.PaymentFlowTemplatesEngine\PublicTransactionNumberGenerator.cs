﻿using BlueTape.CompanyClient.Abstractions;
using BlueTape.CompanyService.Companies;
using BlueTape.Document.DataAccess.EF.Abstractions;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Abstractions;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Constants;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Enums;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Extensions;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Models;
using BlueTape.Utilities.Converters;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.PaymentFlowTemplatesEngine;

public class PublicTransactionNumberGenerator(ISequencesRepository sequencesRepository,
    ICompanyHttpClient companyHttpClient,
    ILogger<PublicTransactionNumberGenerator> logger) : IPublicTransactionNumberGenerator
{
    public async Task<string?> GetNextPublicTransactionNumber(CreatePublicTransactionNumberModel createPublicTransactionNumberModel, CancellationToken ct)
    {
        var transactionTypeIdentifier = GetTransactionTypeIdentifier(createPublicTransactionNumberModel);
        if (!transactionTypeIdentifier.HasValue) return null;

        var numericTransactionTypeIdentifier = (int)transactionTypeIdentifier;
        var companyId = GetParticipantCompanyId(transactionTypeIdentifier.Value, createPublicTransactionNumberModel.PayeeId, createPublicTransactionNumberModel.PayerId);

        CompanyPaymentDetailsModel? companyPaymentDetails;

        try
        {
            companyPaymentDetails = await companyHttpClient.GetCompanyPaymentDetailsByIdAsync(companyId, ct);
        }
        catch (Exception ex)
        {
            logger.LogError("Transaction identifier generation, error during getting company identifier, null value will be returned. Message: {message}", ex.Message);
            return null;
        }

        if (companyPaymentDetails == null)
        {
            logger.LogError("Transaction identifier generation, error during getting company identifier, null value will be returned. ");
            return null;
        }

        var companyIdentifier = companyPaymentDetails.PublicIdentifier;

        if (!IsCompanyIdentifierValid(companyIdentifier))
        {
            logger.LogError("Transaction identifier generation, " +
                "error during company identifier generation, generated identifier: {companyIdentifier}," +
                "null value will be returned", companyIdentifier?.NonBlankValueOf());
            return null;
        }

        var nextSequence = await sequencesRepository.GetNextTransactionIdentifierSequenceNumber(ct);
        var transactionIdentifier = nextSequence.ConvertToBase36(PaymentFlowTemplatesEngineConstants.PublicIdentifierLength);

        var publicTransactionNumber = $"{companyIdentifier}{numericTransactionTypeIdentifier}{transactionIdentifier}";

        if (!IsPublicTransactionNumberValid(publicTransactionNumber))
        {
            logger.LogError("Transaction identifier generation, " +
                "generated number does not have expected length: {length}, generated identifier: {companyIdentifier}," +
                "null value will be returned", PaymentFlowTemplatesEngineConstants.PublicTransactionNumberLength, publicTransactionNumber);

            return null;
        }

        return publicTransactionNumber;
    }

    private static TransactionTypeIdentifier? GetTransactionTypeIdentifier(CreatePublicTransactionNumberModel createPublicTransactionNumberModel)
    {
        if (IsCustomerParticipantStep(createPublicTransactionNumberModel)) return TransactionTypeIdentifier.CustomerParticipant;
        if (IsMerchantParticipantStep(createPublicTransactionNumberModel)) return TransactionTypeIdentifier.MerchantParticipant;
        if (IsBorrowerParticipantStep(createPublicTransactionNumberModel)) return TransactionTypeIdentifier.BorrowerParticipant;
        if (IsFinalPaymentParticipantStep(createPublicTransactionNumberModel)) return TransactionTypeIdentifier.FinalPayment;

        return null;
    }

    private static bool IsCustomerParticipantStep(CreatePublicTransactionNumberModel createPublicTransactionNumberModel)
    {
        return (createPublicTransactionNumberModel.TemplateCode == DomainConstants.InvoicePayment
                || createPublicTransactionNumberModel.TemplateCode == DomainConstants.InvoiceDisbursementV2
                || createPublicTransactionNumberModel.TemplateCode == DomainConstants.InvoicePaymentV2
                || createPublicTransactionNumberModel.TemplateCode == DomainConstants.ManualPaymentPull)
               && createPublicTransactionNumberModel.StepName.Contains("Customer");
    }

    private static bool IsMerchantParticipantStep(CreatePublicTransactionNumberModel createPublicTransactionNumberModel)
    {
        return (createPublicTransactionNumberModel.TemplateCode == DomainConstants.InvoicePayment
                || createPublicTransactionNumberModel.TemplateCode == DomainConstants.InvoiceDisbursementV2
                || createPublicTransactionNumberModel.TemplateCode == DomainConstants.InvoicePaymentV2
                || createPublicTransactionNumberModel.TemplateCode == DomainConstants.InvoicePaymentCard
                || createPublicTransactionNumberModel.TemplateCode == DomainConstants.FactoringDisbursement
                || createPublicTransactionNumberModel.TemplateCode == DomainConstants.DrawDisbursement)
             && createPublicTransactionNumberModel.StepName.Contains("Merchant");
    }

    private static bool IsBorrowerParticipantStep(CreatePublicTransactionNumberModel createPublicTransactionNumberModel)
    {
        return (createPublicTransactionNumberModel.TemplateCode == DomainConstants.DrawRepayment
                || createPublicTransactionNumberModel.TemplateCode == DomainConstants.SubscriptionFeePayment)
               && (createPublicTransactionNumberModel.StepName.Contains("Borrower")
                   || createPublicTransactionNumberModel.StepName.Contains("CollectFrom")) ;
    }

    private static bool IsFinalPaymentParticipantStep(CreatePublicTransactionNumberModel createPublicTransactionNumberModel)
    {
        return (createPublicTransactionNumberModel.TemplateCode is DomainConstants.FinalPayment or DomainConstants.FinalPaymentV2 or DomainConstants.FactoringFinalPayment)
            && createPublicTransactionNumberModel.StepName.Contains("Merchant");
    }

    private static string GetParticipantCompanyId(TransactionTypeIdentifier transactionTypeIdentifier, string? payeeId, string? payerId)
    {
        return transactionTypeIdentifier switch
        {
            TransactionTypeIdentifier.CustomerParticipant or TransactionTypeIdentifier.BorrowerParticipant => payerId ?? string.Empty,
            TransactionTypeIdentifier.MerchantParticipant or TransactionTypeIdentifier.FinalPayment => payeeId ?? string.Empty,
            _ => string.Empty
        };
    }

    private static bool IsCompanyIdentifierValid(string? companyIdentifier)
    {
        return !string.IsNullOrEmpty(companyIdentifier) && companyIdentifier.Length == PaymentFlowTemplatesEngineConstants.CompanyIdentifierLength;
    }

    private static bool IsPublicTransactionNumberValid(string? publicTransactionNumber)
    {
        return !string.IsNullOrEmpty(publicTransactionNumber) && publicTransactionNumber.Length == PaymentFlowTemplatesEngineConstants.PublicTransactionNumberLength;
    }
}
