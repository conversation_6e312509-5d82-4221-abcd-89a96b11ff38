﻿using BlueTape.Common.ExceptionHandling.Exceptions;
using BlueTape.PaymentService.Application.Abstractions.Senders;
using BlueTape.PaymentService.Application.Models.Events.Transaction;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Messages;
using BlueTape.ServiceBusMessaging.Attributes;
using MediatR;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.Application.Handlers.Events.Transaction;

public class TransactionRecalledEventHandler(
    IPaymentTransactionRepository paymentTransactionRepository,
    IPaymentTransactionHistoryRepository paymentTransactionHistoryRepository,
    INotificationMessageSender notificationMessageSender,
    ILogger<TransactionRecalledEventHandler> logger) :
    BaseTransactionEventHandler(paymentTransactionRepository, paymentTransactionHistoryRepository, logger),
    IRequestHandler<TransactionRecalledEvent>
{
    protected override IEnumerable<TransactionStatus> AllowedTransactionStatus => new[]
    {
        TransactionStatus.Recalled,
    };

    public Task Handle(TransactionRecalledEvent request, CancellationToken cancellationToken)
        => base.Handle(request, cancellationToken);

    protected override async Task HandleAch(PaymentTransactionEntity transaction, CancellationToken ct)
    {
        if (transaction.PaymentRequest is null) throw new VariableNullException(nameof(transaction.PaymentRequest));

        switch (transaction.PaymentRequest.FlowTemplateCode)
        {
            case DomainConstants.DrawRepaymentCard:
            case DomainConstants.DrawRepayment:
                await HandleAchDrawRepayment(transaction, ct);
                break;
            case DomainConstants.InvoicePaymentCard:
            case DomainConstants.InvoicePayment:
            case DomainConstants.InvoicePaymentV2:
            case DomainConstants.InvoiceDisbursementV2:
                await HandleAchPayNow(transaction, ct);
                break;
            case DomainConstants.FinalPayment:
            case DomainConstants.FinalPaymentV2:
            case DomainConstants.FactoringFinalPayment:
            case DomainConstants.FactoringDisbursement:
                await HandleTransactionHistoryNotifications(transaction, ct);
                break;
            default:
                logger.LogWarning($"Unable to handle payment request of type: {transaction.PaymentRequest.FlowTemplateCode}, paymentRequestId: {transaction.PaymentRequestId}");
                break;
        }

    }

    private async Task HandleAchPayNow(PaymentTransactionEntity transaction, CancellationToken ct)
    {
        await HandleTransactionHistoryNotifications(transaction, ct);
    }

    private async Task HandleAchDrawRepayment(PaymentTransactionEntity transaction, CancellationToken ct)
    {
        await HandleTransactionHistoryNotifications(transaction, ct);
    }

    private async Task HandleTransactionHistoryNotifications(PaymentTransactionEntity transaction, CancellationToken ct)
    {
        var transactionHistory = (await paymentTransactionHistoryRepository.GetByTransactionId(transaction.Id, ct))
            .OrderBy(x => x.CreatedAt)
            .Reverse()
            .FirstOrDefault();

        var messagesToSend = new ServiceBusMessageBt<NotificationMessagePayloadV2>(new NotificationMessagePayloadV2
        {
            Id = transactionHistory!.Id,
            NotificationType = NotificationType.TransactionHistoryUpdate
        });

        await notificationMessageSender.SendMessage(messagesToSend, ct);
    }
}