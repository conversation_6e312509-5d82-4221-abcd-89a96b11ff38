﻿using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.PaymentService.API.Attributes;
using BlueTape.PaymentService.API.ViewModels;
using BlueTape.PaymentService.DataAccess.External.Abstractions.Services;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.TestUtilities.Abstractions.Services;
using BlueTape.PaymentService.TestUtilities.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json;

namespace BlueTape.PaymentService.API.Controllers;

[ExcludeFromCodeCoverage(Justification = "Controllers are not required to be tested at the moment")]
[Authorize]
[ApiController]
[DevelopmentOnly] //Disable for production
[Route("/tests")]
public class TestingController(
    ITestingService testingService,
    IPaymentFlowTestService paymentFlowTestingService,
    ICardPaymentReportTestingService cardPaymentTestingService,
    ILmsExternalService lmsExternalServices
    ) : ControllerBase
{
    [HttpPost("create-invoice")]
    public IActionResult CreateInvoice([FromBody] CreateInvoiceRequest request, CancellationToken ct)
    {
        return Ok(testingService.CreateInvoice(request.SupplierEmail, request.CustomerEmail, request.Amount, ct));
    }

    [HttpPost("create-payment-request")]
    public async Task CreatePaymentRequest(
        [FromBody] CreateInvoicePaymentV2Request request,
        [FromHeader] string createdBy,
        CancellationToken ct)
    {
        var loan = await lmsExternalServices.GetLoanDetailedById(Guid.Parse(request.DrawId), ct);

        await testingService.SendInvoicePaymentV2RequestMessage(request, createdBy, PaymentRequestType.InvoicePaymentV2, ct);
    }

    [HttpPost("create-payment-request-message")]
    public async Task<IActionResult> CreatePaymentRequestMessage([FromBody] CreateInvoiceRequest request, PaymentRequestType type, string? invoiceId, CancellationToken ct)
    {
        switch (type)
        {
            case PaymentRequestType.InvoicePayment:
                return Ok(await testingService.CreatePaymentRequest(request.SupplierEmail, request.CustomerEmail, request.Amount, invoiceId, ct, onlyMessage: true));
            case PaymentRequestType.InvoicePaymentV2:
            case PaymentRequestType.InvoiceDisbursementV2:
                return Ok(await testingService.CreatePaymentRequestV2Flow(request.SupplierEmail, request.CustomerEmail, request.Amount, invoiceId, ct, onlyMessage: true));
            case PaymentRequestType.FinalPayment:
            case PaymentRequestType.FinalPaymentV2:
            case PaymentRequestType.FactoringDisbursement:
            case PaymentRequestType.FactoringFinalPayment:
            case PaymentRequestType.DrawDisbursement:
                var message = await testingService.CreateDisbursementPayment(request.SupplierEmail, request.CustomerEmail, request.Amount, type, invoiceId, ct, onlyMessage: true);
                return Ok(JsonSerializer.Serialize(message));
            case PaymentRequestType.DrawRepayment:
                var drMessage = await testingService.CreateDrawRepayment(request.SupplierEmail, request.CustomerEmail, request.Amount, invoiceId, ct, onlyMessage: true);
                return Ok(JsonSerializer.Serialize(drMessage));
            case PaymentRequestType.DrawRepaymentCard:
                var drсMessage = await testingService.CreateDrawRepaymentCard(request.SupplierEmail, request.CustomerEmail, request.Amount, invoiceId, ct, onlyMessage: true);
                return Ok(JsonSerializer.Serialize(drсMessage));
            case PaymentRequestType.DrawRepaymentManual:
                var drmMessage = await testingService.CreateDrawRepaymentManual(request.SupplierEmail, request.CustomerEmail, request.Amount, invoiceId, ct, onlyMessage: true);
                return Ok(JsonSerializer.Serialize(drmMessage));
            case PaymentRequestType.SubscriptionFeePayment:
                var sfMessage = await testingService.CreateSubscriptionFeePayment(request.SupplierEmail, request.CustomerEmail, request.Amount, invoiceId, ct, onlyMessage: true);
                return Ok(JsonSerializer.Serialize(sfMessage));

            default:
                throw new ArgumentException("Wrong payment request type");
        }
    }

    [HttpPost("create-payment-request-old")]
    public async Task<IActionResult> CreatePaymentRequest([FromBody] CreateInvoiceRequest request, PaymentRequestType type, string? invoiceId, CancellationToken ct)
    {
        switch (type)
        {
            case PaymentRequestType.InvoicePayment:
                return Ok(await testingService.CreatePaymentRequest(request.SupplierEmail, request.CustomerEmail, request.Amount, invoiceId, ct));
            case PaymentRequestType.InvoicePaymentV2:
            case PaymentRequestType.InvoiceDisbursementV2:
                return Ok(await testingService.CreatePaymentRequestV2Flow(request.SupplierEmail, request.CustomerEmail, request.Amount, invoiceId, ct));
            case PaymentRequestType.FinalPayment:
            case PaymentRequestType.FinalPaymentV2:
            case PaymentRequestType.FactoringDisbursement:
            case PaymentRequestType.FactoringFinalPayment:
            case PaymentRequestType.DrawDisbursement:
                var message = await testingService.CreateDisbursementPayment(request.SupplierEmail, request.CustomerEmail, request.Amount, type, invoiceId, ct);
                return Ok(JsonSerializer.Serialize(message));
            case PaymentRequestType.DrawRepayment:
                var drMessage = await testingService.CreateDrawRepayment(request.SupplierEmail, request.CustomerEmail, request.Amount, invoiceId, ct);
                return Ok(JsonSerializer.Serialize(drMessage));
            case PaymentRequestType.DrawRepaymentCard:
                var drсMessage = await testingService.CreateDrawRepaymentCard(request.SupplierEmail, request.CustomerEmail, request.Amount, invoiceId, ct);
                return Ok(JsonSerializer.Serialize(drсMessage));
            case PaymentRequestType.DrawRepaymentManual:
                var drmMessage = await testingService.CreateDrawRepaymentManual(request.SupplierEmail, request.CustomerEmail, request.Amount, invoiceId, ct);
                return Ok(JsonSerializer.Serialize(drmMessage));
            default:
                throw new ArgumentException("Wrong payment request type");
        }
    }

    [HttpPost("create-factoring-payment-flow")]
    public async Task<IActionResult> CreateFactoringFlow([FromBody] CreateInvoiceRequest request, CancellationToken ct)
    {
        return Ok(await testingService.CreateFactoringFlow(request.SupplierEmail, request.CustomerEmail, request.Amount, ct));
    }

    [HttpPost("create-trade-credit-payment-flow")]
    public async Task<IActionResult> CreateTradeCreditFlow([FromBody] CreateInvoiceRequest request, CancellationToken ct)
    {
        return Ok(await testingService.CreateTradeCreditFlow(request.SupplierEmail, request.CustomerEmail, request.Amount, ct));
    }

    [HttpPost("create-bunch-of-payment-requests")]
    public async Task<IActionResult> CreateBunchOfPaymentRequest([FromBody] CreateInvoiceRequest request, CancellationToken ct)
    {
        await testingService.CreateBunchOfPaymentRequests(request.SupplierEmail, request.CustomerEmail, request.Amount, ct);

        return Ok();
    }

    [HttpGet("move-payment-request-status/{id}")]
    public async Task<string> MoveStatus([FromRoute] Guid id, CancellationToken ct, [FromQuery] TransactionStatus status = TransactionStatus.Cleared)
    {
        return await testingService.InitializeAionTransactionsStatusMovement(id, status, ct);
    }

    [HttpGet("move-payment-request-status/last-week-payments")]
    public async Task<string> MoveBunchOfStatuses(CancellationToken ct)
    {
        return await testingService.InitializeStatusMovementForBanchOfPayments(ct);
    }

    [HttpGet("generate-tabapay-report/invoice/{invoiceIdOrNumber}")]
    public async Task GenerateTabapayReport([FromRoute] string invoiceIdOrNumber, CancellationToken ct)
    {
        await cardPaymentTestingService.ExecuteCardPaymentFlow(invoiceIdOrNumber, ct);
    }

    [HttpGet("execute-payment-request/{paymentRequestId}")]
    public async Task ExecutePaymentRequest([FromRoute] Guid paymentRequestId, CancellationToken ct)
    {
        await paymentFlowTestingService.ExecutePaymentRequest(paymentRequestId, ct);
    }

    [HttpGet("change-payment-subscription")]
    public async Task<IActionResult> ChangePaymentSubscription([FromQuery] Guid paymentRequestId, [FromQuery] PaymentSubscriptionType newSubscriptionType, CancellationToken ct)
    {
        var result = await testingService.ChangePaymentSubscription(paymentRequestId, newSubscriptionType, ct);
        return Ok(result);
    }
}
