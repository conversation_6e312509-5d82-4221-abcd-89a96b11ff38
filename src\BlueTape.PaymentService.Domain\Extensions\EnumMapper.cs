﻿using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Enums.Legacy;

namespace BlueTape.PaymentService.Domain.Extensions;

public static class EnumMapper
{
    public static OperationStatus MapFromPaymentRequestStatusToOperationStatus(
        this PaymentRequestStatus? paymentRequestStatus)
    {
        if (paymentRequestStatus == null) throw new ArgumentNullException(nameof(paymentRequestStatus));

        return MapFromPaymentRequestStatus(paymentRequestStatus.Value);
    }

    public static OperationStatus MapFromPaymentRequestStatusToOperationStatus(
        this PaymentRequestStatus paymentRequestStatus)
    {
        return MapFromPaymentRequestStatus(paymentRequestStatus);
    }

    public static LegacyTransactionStatus MapFromPaymentTransactionStatusToLegacyTransactionStatus(
        this TransactionStatus transactionStatus)
    {
        return MapFromPaymentTransactionStatus(transactionStatus);
    }

    public static StatusDataTransactionStatus MapFromLegacyTransactionStatusToStatusDataTransactionStatus(
        this TransactionStatus transactionStatus)
    {
        return transactionStatus switch
        {
            TransactionStatus.Aborted or TransactionStatus.Error or TransactionStatus.Canceled or TransactionStatus.Failed or TransactionStatus.Recalled => StatusDataTransactionStatus.REJECTED,
            TransactionStatus.Placed or TransactionStatus.Processing or TransactionStatus.Scheduled => StatusDataTransactionStatus.PENDING,
            TransactionStatus.Cleared or TransactionStatus.Processed => StatusDataTransactionStatus.PROCESSED,
            _ => throw new ArgumentOutOfRangeException(nameof(transactionStatus), transactionStatus, "No suitable mapping for transaction status"),
        };
    }

    public static LegacyTransactionStatus MapFromPaymentTransactionStatusToLegacyTransactionStatus(
        this TransactionStatus? transactionStatus)
    {
        if (transactionStatus == null) throw new ArgumentNullException(nameof(transactionStatus));
        return MapFromPaymentTransactionStatus(transactionStatus.Value);
    }

    private static LegacyTransactionStatus MapFromPaymentTransactionStatus(TransactionStatus transactionStatus)
    {
        return transactionStatus switch
        {
            TransactionStatus.Error or TransactionStatus.Failed or TransactionStatus.Aborted => LegacyTransactionStatus.ERROR,
            TransactionStatus.Canceled or TransactionStatus.Recalled => LegacyTransactionStatus.CANCELED,
            TransactionStatus.Placed => LegacyTransactionStatus.PENDING,
            TransactionStatus.Processing or TransactionStatus.Processed => LegacyTransactionStatus.PROCESSING,
            TransactionStatus.Cleared => LegacyTransactionStatus.SUCCESS,
            _ => throw new ArgumentOutOfRangeException(nameof(transactionStatus), transactionStatus, "No suitable mapping for transaction status"),
        };
    }

    private static OperationStatus MapFromPaymentRequestStatus(PaymentRequestStatus paymentRequestStatus)
    {
        return paymentRequestStatus switch
        {
            PaymentRequestStatus.Failed or PaymentRequestStatus.Aborted or PaymentRequestStatus.Cancelled => OperationStatus.FAIL,
            PaymentRequestStatus.Processing or PaymentRequestStatus.Requested or PaymentRequestStatus.Processed => OperationStatus.PROCESSING,
            PaymentRequestStatus.Settled => OperationStatus.SUCCESS,
            _ => throw new ArgumentOutOfRangeException(nameof(paymentRequestStatus), paymentRequestStatus, "No suitable mapping for operation status"),
        };
    }

    public static string MapFromPaymentRequestTypeToOperationType(
        this PaymentRequestType paymentRequestType)
    {
        return paymentRequestType switch
        {
            PaymentRequestType.InvoicePayment or PaymentRequestType.InvoicePaymentV2 or
                PaymentRequestType.InvoiceDisbursementV2 or PaymentRequestType.DrawDisbursement => LegacyPaymentFlowConstants.InvoicePaymentOperationType,
            PaymentRequestType.DrawRepayment or PaymentRequestType.DrawRepaymentCard or PaymentRequestType.DrawRepaymentManual => LegacyPaymentFlowConstants.DrawRepaymentOperationType,
            PaymentRequestType.FinalPayment or PaymentRequestType.FinalPaymentV2 => LegacyPaymentFlowConstants.InvoiceFinalPayment,
            PaymentRequestType.FactoringDisbursement => LegacyPaymentFlowConstants.FactoringDisbursementOperationType,
            PaymentRequestType.FactoringFinalPayment => LegacyPaymentFlowConstants.FactoringFinalPaymentOperationType,
            PaymentRequestType.ManualPaymentPull => LegacyPaymentFlowConstants.AchPullOperationType,
            PaymentRequestType.SubscriptionFeePayment => LegacyPaymentFlowConstants.InvoicePaymentOperationType,
            _ => throw new ArgumentOutOfRangeException(nameof(paymentRequestType), paymentRequestType, "No suitable mapping for operation type"),
        };
    }

    public static AionPaymentMethodType MapPaymentMethodToAionType(this PaymentMethod method)
    {
        return method switch
        {
            PaymentMethod.Ach => AionPaymentMethodType.ACH,
            PaymentMethod.SameDayAch => AionPaymentMethodType.ACH,
            PaymentMethod.Wire => AionPaymentMethodType.WIRE,
            PaymentMethod.Instant => AionPaymentMethodType.INSTANT,
            _ => AionPaymentMethodType.DEFAULT
        };
    }
}
