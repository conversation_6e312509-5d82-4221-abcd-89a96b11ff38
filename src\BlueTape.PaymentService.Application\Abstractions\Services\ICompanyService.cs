﻿using BlueTape.CompanyService.Companies;
using BlueTape.PaymentService.Application.Models.Company;

namespace BlueTape.PaymentService.Application.Abstractions.Services;
public interface ICompanyService
{
    Task<IEnumerable<CompanyModel>?> GetCompaniesByCustomerId(string customerId, CancellationToken ct);

    Task<List<CompanyModel>> GetForbiddenCompanies(CancellationToken ct);
    Task<CompanyModel?> AddCompanyToForbiddenList(ForbidCompanyRequest request, string createdBy, CancellationToken ct);
    Task RemoveCompanyFromForbiddenList(string companyId, CancellationToken ct);
    Task<bool> AreCompaniesPaymentsForbidden(IEnumerable<string> companyIds, CancellationToken ct);
    Task<List<CompanyModel>> SearchCompaniesByName(string searchTerm, CancellationToken ct);
}
