﻿using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.ManualPaymentPull;
using BlueTape.PaymentService.CompatibilityService.Extensions;
using BlueTape.PaymentService.CompatibilityService.Models;
using BlueTape.PaymentService.CompatibilityService.Services.Base;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Operation;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Transaction;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Enums.Legacy;
using BlueTape.PaymentService.Domain.Extensions;
using MongoDB.Bson;
using System.Globalization;
using TinyHelpers.Extensions;

namespace BlueTape.PaymentService.CompatibilityService.Services.ManualPaymentPull;

public class ManualPaymentPullCompatibilityMapper(IBankAccountRepository bankAccountRepository) : BaseCompatibilityMapper(bankAccountRepository), IManualPaymentPullCompatibilityMapper
{
    public override OperationEntity MapFromPaymentRequestToOperation(PaymentRequestEntity paymentRequest, string ownerId)
    {
        var operationStatus = paymentRequest.GetDrawRepaymentOperationStatus().ToString();
        var firstTransaction = paymentRequest.Transactions
            .Where(x => x.TransactionType == PaymentTransactionType.AchPull)
            .MinBy(x => x.CreatedAt);

        return new OperationEntity()
        {
            BlueTapeId = ObjectId.GenerateNewId().ToString()!,
            OwnerId = ownerId,
            Status = operationStatus,
            Type = paymentRequest.RequestType.MapFromPaymentRequestTypeToOperationType(),
            Amount = paymentRequest.Amount,
            Date = paymentRequest.Date.ToDateTime(new TimeOnly(), DateTimeKind.Utc),
            Metadata = new OperationMetadataEntity
            {
                PayerId = paymentRequest.PayerId ?? string.Empty,
                PaymentMethod = paymentRequest.PaymentMethod.ToString().ToLower(),
                PaymentDate = paymentRequest.Date.ToString(),
            },
            PaymentRequestId = paymentRequest.Id.ToString(),
            CreatedBy = DomainConstants.PaymentService,
            FirstTransactionDate = firstTransaction?.CreatedAt,
            PaymentProvider = PaymentProvider.Aion.ToString(),
        };
    }

    public async Task<List<TransactionEntity>> MapFromPaymentTransactionsToLegacyTransactions(PaymentRequestEntity paymentRequest, OperationEntity operation, CancellationToken ctx)
    {
        var transactions = paymentRequest.Transactions;
        var operationId = operation.BlueTapeId;
        var payerId = paymentRequest.PayerId;
        var legacyTransactions = new List<TransactionEntity>();

        await transactions.ForEachAsync(async transaction =>
        {
            var metadata = await GetTransactionMetadata(transaction, transaction.Amount, ctx);

            var legacyTransaction = new TransactionEntity()
            {
                OperationId = operationId,
                Type = PaymentTransactionType.AchPull.ToString().ToLower(),
                PayerId = payerId,
                Amount = transaction.Amount,
                Currency = transaction.Currency,
                Fee = 0,
                PaymentMethod = PaymentMethod.Ach.ToString().ToLower(),
                Status = OperationStatus.PLACED.ToString(),
                CreatedBy = DomainConstants.PaymentService,
                Provider = PaymentProvider.Aion.ToString(),
                PaymentTransactionId = transaction.Id.ToString(),
                Date = transaction.Date.ToDateTime(new TimeOnly()),
                Metadata = metadata,
            };

            legacyTransactions.Add(legacyTransaction);
        }, cancellationToken: ctx);

        return legacyTransactions;
    }

    public override Task<UpdateTransactionEntity> MapSyncModelToUpdateTransactionEntity(SyncTransactionModel syncTransactionModel, CancellationToken ctx)
    {
        var updateTransactionEntity = new UpdateTransactionEntity
        {
            Date = syncTransactionModel.Date,
            Status = syncTransactionModel.Status.MapFromPaymentTransactionStatusToLegacyTransactionStatus().ToString(),
            StatusDataAccountId = syncTransactionModel.ReceiverAccountId,
            StatusDataTransactionNumber = syncTransactionModel.ReferenceNumber,
            OriginalReference = syncTransactionModel.TransactionNumber,
            MetadataTransactionNumber = syncTransactionModel.ReferenceNumber,
            ApiDateTime = syncTransactionModel.ExecutedAt,
            UpdatedBy = DomainConstants.PaymentService,
            StatusCode = syncTransactionModel.StatusCode,
            PaymentTransactionId = syncTransactionModel.PaymentTransactionId,
            Provider = syncTransactionModel.Provider,
            PublicTransactionNumber = syncTransactionModel.PublicTransactionNumber,
            StatusReason = syncTransactionModel.StatusReason,
            StatusDataTransactionStatus = syncTransactionModel.StatusDataTransactionStatus
        };

        return Task.FromResult(updateTransactionEntity);
    }

    protected override Task<TransactionMetadataEntity> GetTransactionMetadata(PaymentTransactionEntity? transaction, decimal legacyTransactionAmount, CancellationToken ctx)
    {
        var transactionType = LegacyTransactionType.PULL;
        var entity = new TransactionMetadataEntity()
        {
            TransactionType = transactionType.ToString(),
            Payload = string.Empty,
            StatusData = new TransactionStatusDataEntity()
            {
                TransactionAmountCents = (double)legacyTransactionAmount * 100,
                Account = new TransactionAccountEntity()
                {
                    AccountId = transaction?.ReceiverAccountId,
                    BalanceCents = 0,
                    HoldBalanceCents = 0,
                    Status = LegacyPaymentFlowConstants.DrawRepaymentTransactionAccountStatus
                },
                TransactionNumber = transaction?.ReferenceNumber,
                TransactionStatus = transaction?.Status.MapFromLegacyTransactionStatusToStatusDataTransactionStatus().ToString(),
                ApiMetadata = new TransactionApiMetadataEntity()
                {
                    DateTime = transaction?.ExecutedAt.ToString(CultureInfo.InvariantCulture),
                    OriginalReference = transaction?.TransactionNumber,
                    Reference = transaction?.TransactionNumber,
                    Type = LegacyPaymentFlowConstants.TransactionApiMetadataPullType
                },
                StatusCode = LegacyPaymentFlowConstants.TransactionMetadataStatusCode,
                OriginalRequestBase64 = string.Empty,
            },
            PublicTransactionNumber = transaction?.PublicTransactionNumber,
        };

        return Task.FromResult(entity);
    }
}
