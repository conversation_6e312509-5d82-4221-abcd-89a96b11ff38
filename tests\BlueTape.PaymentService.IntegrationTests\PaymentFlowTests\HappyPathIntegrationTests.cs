﻿using BlueTape.Integrations.Aion.Infrastructure.Constants;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Extensions;
using BlueTape.PaymentService.IntegrationTests.PaymentFlowTests.Base;
using BlueTape.PaymentService.IntegrationTests.TestConstants;
using BlueTape.PaymentService.IntegrationTests.TestData;

namespace BlueTape.PaymentService.IntegrationTests.PaymentFlowTests;

[Collection(Configuration.SequentialExecution)]
public class HappyPathIntegrationTests : BasePaymentFlowTest
{
    private readonly int firstStepNumber = 1;
    private readonly int happyPathStepsCount = TemplateOptions.InvoicePaymentTemplateOptions.Steps.Length;

    #region Generic tests

    [Theory]
    [InlineData(DomainConstants.InvoicePayment, PaymentMethod.Ach)]
    [InlineData(DomainConstants.InvoicePaymentV2, PaymentMethod.Ach)]
    [InlineData(DomainConstants.DrawRepayment, PaymentMethod.Ach)]
    [InlineData(DomainConstants.FinalPayment, PaymentMethod.Ach)]
    [InlineData(DomainConstants.FinalPaymentV2, PaymentMethod.Ach)]
    [InlineData(DomainConstants.FactoringFinalPayment, PaymentMethod.Ach)]
    [InlineData(DomainConstants.InvoiceDisbursementV2, PaymentMethod.Ach)]
    [InlineData(DomainConstants.FactoringDisbursement, PaymentMethod.Ach)]
    [InlineData(DomainConstants.DrawDisbursement, PaymentMethod.Ach)]
    [InlineData(DomainConstants.DrawRepaymentCard, PaymentMethod.Ach)]
    [InlineData(DomainConstants.DrawRepaymentManual, PaymentMethod.Ach)]
    [InlineData(DomainConstants.InvoicePaymentCard, PaymentMethod.Ach)]
    [InlineData(DomainConstants.SubscriptionFeePayment, PaymentMethod.Ach)]
    public async Task Payment_ValidRequest_ShouldExecutePayment(string templateCode, PaymentMethod paymentMethod)
    {
        var message = PaymentRequestData.CreateValidPaymentRequest(templateCode, paymentMethod: paymentMethod.ToString());

        var paymentRequest = await CreateAndAssertPayment(message);

        var steps = TemplateOptions.GetTemplateSteps(templateCode);
        for (int i = firstStepNumber; i <= steps; i++)
        {
            await ExecuteAndAssertTestCommandCycle(paymentRequest.Id, i);
        }

        var details = await GetPaymentRequestById(paymentRequest.Id);
        var externalTransactions = details.Transactions
            .Where(x => x.TransactionType is PaymentTransactionType.AchPull || x.TransactionType.IsPushTransaction())
            .ToList();
        var transactionNumbers = externalTransactions.Select(x => x.PublicTransactionNumber);

        transactionNumbers.ShouldAllBe(x => x != null);
        details.Status.ShouldBe(PaymentRequestStatus.Settled);

        await ReceiveAndExecuteNotificationMessages();

        var notifications = await GetNotificationsByPaymentRequestId(paymentRequest.Id);
        notifications.ShouldNotBeNull();
    }

    [Theory]
    [InlineData(DomainConstants.FactoringFinalPayment)]
    [InlineData(DomainConstants.InvoicePayment)]
    [InlineData(DomainConstants.InvoicePaymentV2)]
    [InlineData(DomainConstants.DrawRepayment)]
    [InlineData(DomainConstants.FinalPayment)]
    [InlineData(DomainConstants.FinalPaymentV2)]
    [InlineData(DomainConstants.InvoiceDisbursementV2)]
    [InlineData(DomainConstants.FactoringDisbursement)]
    [InlineData(DomainConstants.DrawDisbursement)]
    [InlineData(DomainConstants.DrawRepaymentCard)]
    [InlineData(DomainConstants.DrawRepaymentManual)]
    [InlineData(DomainConstants.InvoicePaymentCard)]
    [InlineData(DomainConstants.SubscriptionFeePayment)]
    public async Task Payment_MultiplePaymentJobExecuting_ShouldNotChangeStatus(string templateCode)
    {
        var message = PaymentRequestData.CreateValidPaymentRequest(templateCode);
        var paymentRequest = await CreateAndAssertPayment(message);
        var paymentRequestId = paymentRequest.Id;

        var details = await GetPaymentRequestById(paymentRequest.Id);
        var steps = TemplateOptions.GetTemplateSteps(templateCode);
        for (int step = firstStepNumber; step <= steps; step++)
        {
            await ExecutePaymentScheduledJob();
            details = await GetPaymentRequestById(paymentRequestId);
            AssertTransactionStatuses(details.Transactions, TransactionStatus.Placed, step);
            AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Pending, step);
            details.Status.ShouldBe(step == 1 ? PaymentRequestStatus.Requested : PaymentRequestStatus.Processing);

            await ExecutePaymentScheduledJob();
            details = await GetPaymentRequestById(paymentRequestId);
            AssertTransactionStatuses(details.Transactions, TransactionStatus.Placed, step);
            AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Pending, step);
            details.Status.ShouldBe(step == 1 ? PaymentRequestStatus.Requested : PaymentRequestStatus.Processing);

            await ExecuteAndAssertTestCommandCycle(paymentRequest.Id, step);
        }

        details = await GetPaymentRequestById(paymentRequest.Id);
        details.Status.ShouldBe(PaymentRequestStatus.Settled);
    }

    #endregion

    #region InvoicePaymentSpecific

    [Fact]
    public async Task Payment_ValidRequestNegativePayableAmount_ShouldExecutePayment()
    {
        var message = PaymentRequestData.CreateValidMultipleInvoicePaymentMessage();
        var paymentRequest = await CreateAndAssertPayment(message);

        var steps = TemplateOptions.InvoicePaymentTemplateOptions.Steps.Length;
        for (int i = firstStepNumber; i <= steps; i++)
        {
            await ExecuteAndAssertTestCommandCycle(paymentRequest.Id, i);
        }

        var details = await GetPaymentRequestById(paymentRequest.Id);
        details.Status.ShouldBe(PaymentRequestStatus.Settled);
    }

    [Fact]
    public async Task Payment_ValidRequestMessageWithZeroAmount_ShouldBeSkippedAndExecutePaymentRequest()
    {
        var happyPathStepsCount = TemplateOptions.InvoicePaymentTemplateOptions.Steps.Length;
        var message = PaymentRequestData.CreateValidInvoicePaymentMessage(feeAmount: 0);
        var paymentRequest = await CreateAndAssertPayment(message);
        var paymentRequestId = paymentRequest!.Id;

        var details = await GetPaymentRequestById(paymentRequest!.Id);

        var transactionWithZeroAmount = details.Transactions.First(x => x.Amount == 0);

        for (int i = firstStepNumber; i <= happyPathStepsCount; i++)
        {
            // Transactions with zero amount will be skipped
            if (i == transactionWithZeroAmount.SequenceNumber)
            {
                var sequence = i;
                details = await GetPaymentRequestById(paymentRequestId);

                details.Status.ShouldBe(sequence == 1 ? PaymentRequestStatus.Requested : PaymentRequestStatus.Processing);

                await ExecutePaymentScheduledJob();
                details = await GetPaymentRequestById(paymentRequestId);
                AssertTransactionStatuses(details.Transactions, TransactionStatus.Placed, sequence);
                AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Pending, sequence);
                details.Status.ShouldBe(sequence == 1 ? PaymentRequestStatus.Requested : PaymentRequestStatus.Processing);

                var commandToExecute = details.PaymentRequestCommands.First(x => x.Status == CommandStatus.Pending);

                await ExecuteCommandManagement(commandToExecute.Id);
                details = await GetPaymentRequestById(paymentRequestId);
                AssertTransactionStatuses(details.Transactions, TransactionStatus.Cleared, sequence);
                AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Executed, sequence);
                details.Status.ShouldBe(PaymentRequestStatus.Processing);

                continue;
            }

            await ExecuteAndAssertTestCommandCycle(paymentRequest.Id, i);
        }

        details = await GetPaymentRequestById(paymentRequest.Id);
        details.Status.ShouldBe(PaymentRequestStatus.Settled);
    }

    [Fact]
    public async Task Payment_MessageWithAchDelay_ShouldNotExecuteLastTransaction()
    {
        var message = PaymentRequestData.CreateValidInvoicePaymentMessage();
        message.PaymentRequestDetails.SellerDetails.PaymentSettings.MerchantAchDelayDays = 2;
        var paymentRequest = await CreateAndAssertPayment(message);
        var sequence = 4;

        var paymentRequestId = paymentRequest.Id;

        for (int i = firstStepNumber; i <= happyPathStepsCount - 1; i++)
        {
            await ExecuteAndAssertTestCommandCycle(paymentRequest.Id, i);
        }

        var details = await GetPaymentRequestById(paymentRequestId);
        details.Status.ShouldBe(PaymentRequestStatus.Processing);

        await ExecutePaymentScheduledJob();
        details = await GetPaymentRequestById(paymentRequestId);
        AssertTransactionStatuses(details.Transactions, TransactionStatus.Placed, sequence);
        AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Placed, sequence);
    }

    [Theory]
    [InlineData(2)]
    [InlineData(3)]
    [InlineData(4)]
    public async Task Payment_ReturnsErrorStatusNonFirst_ShouldSetFailedStatus(int failedStepNum)
    {
        var message = PaymentRequestData.CreateValidInvoicePaymentMessage();
        var paymentRequest = await CreateAndAssertPayment(message);

        for (int i = firstStepNumber; i < failedStepNum; i++)
        {
            await ExecuteAndAssertTestCommandCycle(paymentRequest.Id, i);
        }

        await ExecuteAndAssertTestCommandCycle(paymentRequest.Id, failedStepNum, finalStatus: TransactionStatus.Error);

        var details = await GetPaymentRequestById(paymentRequest.Id);
        details.Status.ShouldBe(PaymentRequestStatus.Failed);
    }

    [Fact]
    public async Task Payment_TransactionExecutedManually_ShouldNotMakeDoublePayment()
    {
        var message = PaymentRequestData.CreateValidInvoicePaymentMessage();
        var paymentRequest = await CreateAndAssertPayment(message);

        await ExecuteAndAssertTestCommandCycle(paymentRequest.Id, 1);
        await ExecuteAndAssertTestCommandCycle(paymentRequest.Id, 2);

        var details = await GetPaymentRequestById(paymentRequest!.Id);

        //Execute the last transaction manually
        var commandIdToExecute = details.PaymentRequestCommands.Single(x => x.StepName == "PushToMerchant").Id;
        await MarkCommandAsExecuted(commandIdToExecute);

        details = await GetPaymentRequestById(paymentRequest.Id);
        details.Status.ShouldBe(PaymentRequestStatus.Processing);

        //Execute the third command
        await ExecuteRetriedTestCommandCycleSimplifiedAssertion(paymentRequest.Id);

        details = await GetPaymentRequestById(paymentRequest.Id);
        details.Status.ShouldBe(PaymentRequestStatus.Settled);
    }

    [Theory]
    [InlineData(DomainConstants.InvoicePayment)]
    [InlineData(DomainConstants.InvoicePaymentV2)]
    [InlineData(DomainConstants.InvoicePaymentCard)]
    [InlineData(DomainConstants.SubscriptionFeePayment)]
    public async Task Payment_RetryFailedTransactions_ShouldExecutePaymentRequest(string templateCode)
    {
        var message = PaymentRequestData.CreateValidPaymentRequest(templateCode);
        var paymentRequest = await CreateAndAssertPayment(message);
        var paymentRequestId = paymentRequest.Id;

        var details = await GetPaymentRequestById(paymentRequest!.Id);

        var steps = TemplateOptions.GetTemplateSteps(templateCode);
        for (int step = firstStepNumber; step <= steps; step++)
        {
            await ExecutePaymentScheduledJob();
            details = await GetPaymentRequestById(paymentRequestId);
            details.Transactions.First(x => x.SequenceNumber == step).Status.ShouldBe(TransactionStatus.Placed);
            details.PaymentRequestCommands.First(x => x.SequenceNumber == step).Status.ShouldBe(CommandStatus.Pending);
            details.Status.ShouldBe(step == 1 ? PaymentRequestStatus.Requested : PaymentRequestStatus.Processing);

            var commandToExecute = details.PaymentRequestCommands.First(x => x.Status == CommandStatus.Pending);

            await ExecuteCommandManagement(commandToExecute.Id);
            details = await GetPaymentRequestById(paymentRequestId);
            details.Transactions.First(x => x.SequenceNumber == step).Status.ShouldBe(TransactionStatus.Processing);
            details.PaymentRequestCommands.First(x => x.SequenceNumber == step).Status.ShouldBe(CommandStatus.Executing);
            details.Status.ShouldBe(PaymentRequestStatus.Processing);

            var transactionIdToUpdate = commandToExecute.TransactionId;
            var transactionToUpdate = details.Transactions.First(x => x.Id == transactionIdToUpdate);

            await ExecuteTransactionStatusUpdateConsumer(GetTransactionStatusMessage(transactionToUpdate, AionStatuses.Sent));
            details = await GetPaymentRequestById(paymentRequestId);
            details.Transactions.First(x => x.SequenceNumber == step).Status.ShouldBe(TransactionStatus.Processed);
            details.PaymentRequestCommands.First(x => x.SequenceNumber == step).Status.ShouldBe(CommandStatus.Executing);
            details.Status.ShouldBe(PaymentRequestStatus.Processing);

            await ExecuteTransactionStatusUpdateConsumer(GetTransactionStatusMessage(transactionToUpdate, AionStatuses.Error));
            details = await GetPaymentRequestById(paymentRequestId);
            details.Transactions.First(x => x.SequenceNumber == step).Status.ShouldBe(TransactionStatus.Failed);
            details.PaymentRequestCommands.First(x => x.SequenceNumber == step).Status.ShouldBe(CommandStatus.Failed);
            details.Status.ShouldBe(PaymentRequestStatus.Failed);

            await RetryFailedTransaction(paymentRequestId.ToString(), transactionIdToUpdate.ToString());

            await ExecuteRetriedTestCommandCycleSimplifiedAssertion(paymentRequest.Id);
        }

        details = await GetPaymentRequestById(paymentRequest.Id);
        details.Status.ShouldBe(PaymentRequestStatus.Settled);
    }

    #endregion

    #region DrawRepaymentSpecific
    /*
    [Fact]

    public async Task Payment_RetryFailedTransaction_ShouldNotRetry()
    {
        var message = PaymentRequestData.CreateValidDrawRepaymentMessage();
        var paymentRequest = await CreateAndAssertPayment(message);
        var transactionIdToUpdate = paymentRequest.Transactions.First().Id;

        await ExecuteAndAssertTestCommandCycle(paymentRequest.Id, firstStepNumber, finalStatus: TransactionStatus.Error);

        await RetryFailedTransaction(paymentRequest.Id.ToString(), transactionIdToUpdate.ToString());

        var details = await GetPaymentRequestById(paymentRequest.Id);
        details.Status.ShouldBe(PaymentRequestStatus.Failed);
        details.Transactions.First(x => x.SequenceNumber == firstStepNumber).Status.ShouldBe(TransactionStatus.Failed);
    }
    */
    #endregion

    #region SPV Arcadia Disbursement Specific (Draw/Factoring Disbursement)

    [Theory]
    [InlineData(DomainConstants.FactoringDisbursement)]
    public async Task SpvDisbursement_ValidMessage_ShouldSetUpNoDelays(string templateCode)
    {
        var message = PaymentRequestData.CreateValidPaymentRequest(templateCode);
        var paymentRequest = await CreateAndAssertPayment(message, skipDelays: false);

        paymentRequest.MerchantAchDelayInBusinessDays.ShouldBe(0);

        await ExecuteAndAssertTestCommandCycle(paymentRequest.Id, firstStepNumber);

        var details = await GetPaymentRequestById(paymentRequest.Id);
        details.Status.ShouldBe(PaymentRequestStatus.Settled);
        AssertTransactionStatuses(details.Transactions, TransactionStatus.Cleared, firstStepNumber);
        AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Executed, firstStepNumber);
    }

    #endregion

    [Theory]
    [InlineData(1)]
    [InlineData(2)]
    [InlineData(3)]
    [InlineData(4)]
    public async Task Payment_MultipleTransactionStatusJobExecuting_ShouldNotChangeStatus(int executedStep)
    {
        var message = PaymentRequestData.CreateValidInvoicePaymentMessage();
        var paymentRequest = await CreateAndAssertPayment(message);

        var paymentRequestId = paymentRequest!.Id;

        for (int i = firstStepNumber; i < executedStep; i++)
        {
            await ExecuteAndAssertTestCommandCycle(paymentRequest.Id, i);
        }

        await ExecutePaymentScheduledJob();
        var details = await GetPaymentRequestById(paymentRequestId);
        AssertTransactionStatuses(details.Transactions, TransactionStatus.Placed, executedStep);
        AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Pending, executedStep);
        details.Status.ShouldBe(executedStep == 1 ? PaymentRequestStatus.Requested : PaymentRequestStatus.Processing);

        var commandToExecute = details.PaymentRequestCommands.First(x => x.Status == CommandStatus.Pending);

        await ExecuteCommandManagement(commandToExecute.Id);
        details = await GetPaymentRequestById(paymentRequestId);
        AssertTransactionStatuses(details.Transactions, TransactionStatus.Processing, executedStep);
        AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Executing, executedStep);
        details.Status.ShouldBe(PaymentRequestStatus.Processing);

        var transactionIdToUpdate = commandToExecute.TransactionId;
        var transactionToUpdate = details.Transactions.First(x => x.Id == transactionIdToUpdate);

        await ExecuteTransactionStatusUpdateConsumer(GetTransactionStatusMessage(transactionToUpdate, AionStatuses.Sent));
        details = await GetPaymentRequestById(paymentRequestId);
        AssertTransactionStatuses(details.Transactions, TransactionStatus.Processed, executedStep);
        AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Executing, executedStep);
        details.Status.ShouldBe(PaymentRequestStatus.Processing);

        await ExecuteTransactionStatusUpdateConsumer(GetTransactionStatusMessage(transactionToUpdate, AionStatuses.Sent));
        details = await GetPaymentRequestById(paymentRequestId);
        AssertTransactionStatuses(details.Transactions, TransactionStatus.Processed, executedStep);
        AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Executing, executedStep);
        details.Status.ShouldBe(PaymentRequestStatus.Processing);
    }

    protected async Task ExecuteRetriedTestCommandCycleSimplifiedAssertion(Guid paymentRequestId)
    {
        var details = await GetPaymentRequestById(paymentRequestId);
        details.Status.ShouldBe(PaymentRequestStatus.Processing);

        await ExecutePaymentScheduledJob();
        details = await GetPaymentRequestById(paymentRequestId);

        var commandToExecute = details.PaymentRequestCommands.First(x => x.Status == CommandStatus.Pending);
        var transactionIdToUpdate = commandToExecute.TransactionId;

        details.Transactions.First(x => x.Id == transactionIdToUpdate).Status.ShouldBe(TransactionStatus.Placed);
        details.PaymentRequestCommands.First(x => x.Id == commandToExecute.Id).Status.ShouldBe(CommandStatus.Pending);
        details.Status.ShouldBe(PaymentRequestStatus.Processing);

        await ExecuteCommandManagement(commandToExecute.Id);
        details = await GetPaymentRequestById(paymentRequestId);
        details.Transactions.First(x => x.Id == transactionIdToUpdate).Status.ShouldBe(TransactionStatus.Processing);
        details.PaymentRequestCommands.First(x => x.Id == commandToExecute.Id).Status.ShouldBe(CommandStatus.Executing);
        details.Status.ShouldBe(PaymentRequestStatus.Processing);

        var transactionToUpdate = details.Transactions.First(x => x.Id == transactionIdToUpdate);

        await ExecuteTransactionStatusUpdateConsumer(GetTransactionStatusMessage(transactionToUpdate, AionStatuses.Sent));
        details = await GetPaymentRequestById(paymentRequestId);
        details.Transactions.First(x => x.Id == transactionIdToUpdate).Status.ShouldBe(TransactionStatus.Processed);
        details.PaymentRequestCommands.First(x => x.Id == commandToExecute.Id).Status.ShouldBe(CommandStatus.Executing);
        details.Status.ShouldBe(PaymentRequestStatus.Processing);

        await ExecuteTransactionStatusUpdateConsumer(GetTransactionStatusMessage(transactionToUpdate, AionStatuses.Cleared));
        details = await GetPaymentRequestById(paymentRequestId);
        details.Transactions.First(x => x.Id == transactionIdToUpdate).Status.ShouldBe(TransactionStatus.Cleared);
        details.PaymentRequestCommands.First(x => x.Id == commandToExecute.Id).Status.ShouldBe(CommandStatus.Executed);
    }
}
