﻿using BlueTape.PaymentService.Domain.Enums;

namespace BlueTape.PaymentService.API.Queries;

public class PaymentRequestFilterQuery
{
    public Guid? Id { get; set; }
    public string? DrawId { get; set; }
    public string? Search { get; set; }
    public List<string>? FlowTemplateCodes { get; set; }
    public DateOnly? From { get; set; }
    public DateOnly? To { get; set; }
    public string? CustomerId { get; set; }
    public string? SellerId { get; set; }
    public string? PayableId { get; set; }
    public List<string>? PaymentRequestStatuses { get; set; }
    public bool? IsConfirmed { get; set; }
    public SortOrder SortOrder { get; set; } = SortOrder.asc;
    public string? SortBy { get; set; }
    public int? Page { get; set; }
    public int? Items { get; set; }
}