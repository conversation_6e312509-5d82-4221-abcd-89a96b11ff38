﻿using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation.Base;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.ManualPaymentPull;

namespace BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;

public interface IManualPaymentPullCreationService : IBasePaymentRequestCreationService
{
    Task<PaymentRequestModel> Add(ManualPaymentPullRequestMessage paymentRequestMessage, string createdBy, CancellationToken ct);
}
