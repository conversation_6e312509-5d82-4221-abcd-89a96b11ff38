using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Domain.Extensions;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.Application.Services;

public class AionDailyLimitResetService(
    IServiceProvider serviceProvider,
    IDateProvider dateProvider,
    ILogger<AionDailyLimitResetService> logger) : BackgroundService
{
    private readonly TimeSpan _checkInterval = TimeSpan.FromMinutes(30); // Check every 30 minutes

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await CheckAndResetDailyLimits(stoppingToken);
                await Task.Delay(_checkInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
                break;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occurred while checking for daily limit reset");
                await Task.Delay(_checkInterval, stoppingToken);
            }
        }
    }

    private async Task CheckAndResetDailyLimits(CancellationToken cancellationToken)
    {
        var currentTime = dateProvider.CurrentDateTime;

        // Check if it's midnight (00:00) on a business day
        if (currentTime.Hour == 0 && currentTime.Minute < 30 && currentTime.IsBusinessDay())
        {
            logger.LogInformation("Resetting Aion daily limits at {CurrentTime}", currentTime);

            using var scope = serviceProvider.CreateScope();
            var paymentConfigService = scope.ServiceProvider.GetRequiredService<IPaymentConfigService>();

            await paymentConfigService.ResetAllAionDailyLimits(cancellationToken);

            logger.LogInformation("Successfully reset Aion daily limits at {CurrentTime}", currentTime);
        }
    }
}
