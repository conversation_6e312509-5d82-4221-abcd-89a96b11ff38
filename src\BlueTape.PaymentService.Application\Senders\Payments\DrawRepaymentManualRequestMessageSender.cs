﻿using BlueTape.PaymentService.Application.Abstractions.Senders.Payments;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepaymentManual;
using BlueTape.ServiceBusMessaging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.Application.Senders.Payments;

public class DrawRepaymentManualMessageSender(
    IConfiguration configuration,
    ILogger<DrawRepaymentManualMessageSender> logger)
    : ServiceBusMessageSender<DrawRepaymentManualRequestMessage>(configuration, logger,
            InfrastructureConstants.PaymentRequestQueueName, InfrastructureConstants.PaymentRequestQueueConnection),
        IDrawRepaymentManualMessageSender;
