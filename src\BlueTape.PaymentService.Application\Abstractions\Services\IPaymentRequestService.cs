﻿using BlueTape.PaymentService.Application.Abstractions.Services.Base;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Application.Models.Base;
using BlueTape.PaymentService.Application.Models.CreatePaymentRequestMessageDtos;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Entities.Filters;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepaymentManual;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.ManualPaymentPull;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;
using System.Text.Json;

namespace BlueTape.PaymentService.Application.Abstractions.Services;

public interface IPaymentRequestService : IGenericService<PaymentRequestModel>
{
    Task<PaginatedResult<PaymentRequestModel>> GetByFilter(PaymentRequestFilter filter, CancellationToken ct);
    Task<IEnumerable<PaymentRequestModel>> GetByPayableId(string payableId, CancellationToken ct);
    Task<IEnumerable<PaymentRequestModel>> GetByDrawId(Guid drawId, CancellationToken ct);
    Task<PaymentRequestModel> CancelPaymentRequest(Guid id, string updatedBy, CancellationToken ct);
    Task<List<PaymentRequestModel>> CancelPaymentRequestByDrawId(Guid id, string updatedBy, CancellationToken ct);
    Task<PaymentRequestCommandEntity> MarkCommandAsExecuted(Guid id, string updatedBy, CancellationToken ct);
    Task<PaymentRequestStatus> CalculatePaymentRequestStatus(Guid paymentRequestId, CancellationToken ctx);
    PaymentRequestStatus CalculatePaymentRequestStatus(List<PaymentRequestCommandModel> paymentRequestCommands);
    Task StartRollBackById(Guid paymentRequestId, string createdBy, CancellationToken ctx);
    Task StartRollBack(List<PaymentRequestCommandEntity> paymentRequestCommands, string createdBy, CancellationToken ctx);
    Task<PaymentRequestModel?> UpdatePauseStatus(Guid paymentRequestId, PausePaymentRequestModel request, string updatedBy, CancellationToken ctx);
    Task<PaymentRequestEntity> CommitPaymentRequestChanges(Guid paymentRequestId, string updatedBy, CancellationToken ct);
    Task<decimal> GetTotalAmount(bool includeProcessingStatus, CancellationToken ct);
    Task ApprovePaymentRequest(Guid paymentRequestId, string confirmedBy, PaymentApprovalRequest paymentApprovalMethod, CancellationToken ct);
    Task<DrawRepaymentManualRequestMessage> SendManualInternalPaymentRequestMessage(CreatePaymentRequestDto request, string userId, CancellationToken ct);
    Task<ManualPaymentPullRequestMessage?> SendManualPaymentPullRequestMessage(JsonElement rawRequest,
        string userId, PaymentRequestType paymentRequestType, CancellationToken ct);
    Task<PaymentRequestModel> MarkPaymentRequestAsSucceeded(Guid paymentRequestId, MarkPaymentRequestSucceededModel request, string updatedBy, CancellationToken ct);
}