using BlueTape.PaymentService.Domain.Entities.Base;

namespace BlueTape.PaymentService.Domain.Entities;

public class PaymentConfigEntity : EntityWithId
{
    public string ConfigKey { get; set; } = string.Empty;
    public string ConfigValue { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string UpdatedBy { get; set; } = string.Empty;
}
