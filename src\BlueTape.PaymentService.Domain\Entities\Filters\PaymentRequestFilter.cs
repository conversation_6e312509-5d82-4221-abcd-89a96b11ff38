﻿using BlueTape.PaymentService.Domain.Enums;

namespace BlueTape.PaymentService.Domain.Entities.Filters;

public class PaymentRequestFilter
{
    public Guid? Id { get; set; }
    public string? DrawId { get; set; }
    public string? Search { get; set; }
    public List<string>? FlowTemplateCodes { get; set; }
    public PaymentRequestType? RequestType { get; set; }
    public DateOnly? DateFrom { get; set; }
    public DateOnly? DateTo { get; set; }
    public string? PayerId { get; set; }
    public string? SellerId { get; set; }
    public string? PayableId { get; set; }
    public List<PaymentRequestStatus>? PaymentRequestStatuses { get; set; }
    public bool? IsConfirmed { get; set; }
    public SortOrder? SortOrder { get; set; }
    public string? SortBy { get; set; }
    public int? PageNumber { get; set; }
    public int? PageSize { get; set; }
    public List<string>? CompanyIds { get; set; }
}