﻿using Amazon.Lambda;
using BlueTape.PaymentService.TestUtilities.Abstractions.Senders;
using BlueTape.PaymentService.TestUtilities.Abstractions.Services;
using BlueTape.PaymentService.TestUtilities.Senders;
using BlueTape.PaymentService.TestUtilities.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace BlueTape.PaymentService.TestUtilities.DI;

public static class DependencyRegistrar
{
    public static void AddTestUtilitiesDependencies(this IServiceCollection services, IConfiguration config)
    {
        services.AddScoped<ITestingService, TestingService>();
        services.AddScoped<IPaymentFlowTestService, PaymentFlowTestingService>();
        services.AddTransient<ICardPaymentReportTestingService, CardPaymentReportTestingService>();

        services.AddScoped<IInvoicePaymentMessageSender, InvoiceRequestMessageSender>();
        services.AddScoped<IDisbursementMessageSender, DisbursementRequestMessageSender>();
        services.AddScoped<IDrawRepaymentMessageSender, DrawRepaymentMessageSender>();
        services.AddScoped<IDrawRepaymentCardMessageSender, DrawRepaymentCardMessageSender>();

        services.AddAWSService<IAmazonLambda>();
        services.AddTransient<IAwsService, AwsService>();
    }
}
