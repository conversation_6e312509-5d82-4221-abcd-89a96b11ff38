﻿using Amazon.SimpleNotificationService.Model;
using BlueTape.AionServiceClient.Abstractions;
using BlueTape.AionServiceClient.Constants;
using BlueTape.AionServiceClient.Exceptions;
using BlueTape.AzureKeyVault.Abstractions;
using BlueTape.Common.ExceptionHandling.Exceptions;
using BlueTape.CompanyClient.Abstractions;
using BlueTape.CompanyClient.Constants;
using BlueTape.CompanyService.BankAccounts;
using BlueTape.CompanyService.Companies;
using BlueTape.Document.DataAccess.EF.Abstractions;
using BlueTape.EmailSender.Abstractions;
using BlueTape.EmailSender.Models;
using BlueTape.Integrations.Aion;
using BlueTape.Integrations.Aion.Ach.CreateAchTransfer;
using BlueTape.Integrations.Aion.Ach.CreateAchTransfer.Response;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.Integrations.Aion.Internal.Internal;
using BlueTape.InvoiceClient.Abstractions;
using BlueTape.InvoiceClient.Constants;
using BlueTape.InvoiceService.Common.Enums;
using BlueTape.InvoiceService.Models.Invoice;
using BlueTape.Ledger.Models.V2;
using BlueTape.LinqpalClient.Abstractions;
using BlueTape.LoanServiceClient.Abstractions.HttpClients;
using BlueTape.LoanServiceClient.Abstractions.Services;
using BlueTape.LoanServiceClient.Configuration;
using BlueTape.LS.DTOs.Loan;
using BlueTape.LS.DTOs.Payment;
using BlueTape.PaymentService.API.Constants;
using BlueTape.PaymentService.Application.Abstractions.Senders;
using BlueTape.PaymentService.Application.Abstractions.Senders.Payments;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Abstractions.Services.Notification;
using BlueTape.PaymentService.Application.Abstractions.Validators;
using BlueTape.PaymentService.Application.Constants;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Service;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.FinalPayment;
using BlueTape.PaymentService.DataAccess.External.Abstractions.Services;
using BlueTape.PaymentService.DataAccess.External.Models;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.CreditApplicationAuthorizationDetails;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.CustomerAccount;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.DrawApproval;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.LoanApplication;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Notification;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Operation;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.PricingPackage;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Messages;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepaymentManual;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.ManualPaymentPull;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;
using BlueTape.PaymentService.IntegrationTests.TestConstants;
using BlueTape.PaymentService.IntegrationTests.TestData;
using BlueTape.Reporting.Application.Abstractions.LMS;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.SNS.SlackNotification;
using BlueTape.SNS.SlackNotification.Models;
using Microsoft.Extensions.Configuration;
using NSubstitute;
using SendGrid;
using SendGrid.Helpers.Mail;
using System.Linq.Expressions;
using System.Net;
using System.Text.Json;
using CompanyModel = BlueTape.CompanyService.Companies.CompanyModel;
using CreateInternalModel = BlueTape.Integrations.Aion.Internal.CreateInternalModel;

namespace BlueTape.PaymentService.IntegrationTests;
public static class MockedServices
{
    private static readonly string[] AionRollBakErrorCodes = ["R06", "R07", "R08", "R29"];
    private static decimal Tolerance = 10;

    public static ILedgerPaymentMessageSender MockLedgerPaymentMessageSender()
    {
        var messageSender = Substitute.For<ILedgerPaymentMessageSender>();

        messageSender.SendMessages(Arg.Any<IEnumerable<ServiceBusMessageBt<CreateOperationModelV2>>>(), Arg.Any<CancellationToken>())
            .Returns(Task.CompletedTask);

        messageSender.SendMessage(Arg.Any<ServiceBusMessageBt<CreateOperationModelV2>>(), Arg.Any<CancellationToken>())
            .Returns(Task.CompletedTask);

        return messageSender;
    }

    public static IOperationSyncMessageSender MockOperationSyncMessageSender()
    {
        var messageSender = Substitute.For<IOperationSyncMessageSender>();

        messageSender.SendMessages(Arg.Any<IEnumerable<ServiceBusMessageBt<SyncOperationMessagePayload>>>(), Arg.Any<CancellationToken>())
            .Returns(Task.CompletedTask);

        messageSender.SendMessage(Arg.Any<ServiceBusMessageBt<SyncOperationMessagePayload>>(), Arg.Any<CancellationToken>())
            .Returns(Task.CompletedTask);

        return messageSender;
    }

    public static ICompanyHttpClient MockCompanyHttpClient()
    {
        var service = Substitute.For<ICompanyHttpClient>();

        service.GetBankAccountsByCompanyIdAsync(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(new List<BankAccountModel>() { new() { Id = $"{Guid.NewGuid()}", AccountType = "Checking" } });

        service.GetCompaniesByIdsAsync(Arg.Any<string[]>(), Arg.Any<CancellationToken>())
            .Returns(arg =>
            {
                var companyModels = arg.Arg<string[]>().Select(id => new CompanyModel
                {
                    Id = id,
                    BankAccounts = [Guid.NewGuid().ToString()],
                    MerchantAutomaticPullAllowed = !id.Equals(Configuration.ForbiddenAutomaticRollbacksMerchantId),
                    Settings = new CompanySettingsModel()
                    {
                        ARAdvance = new ArAdvanceModel()
                        {
                            IsEnabled = true,
                        }
                    }
                }).ToList();


                var customerCompany = companyModels.FirstOrDefault(x => x.Id == Configuration.CustomerCompanyIdWithValidBankAccount);
                if (customerCompany is not null)
                    customerCompany.BankAccounts = [Configuration.ValidBankAccountId];

                var merchantCompany = companyModels.FirstOrDefault(x => x.Id == Configuration.MerchantCompanyId || x.Id == Configuration.ForbiddenAutomaticRollbacksMerchantId);
                if (merchantCompany is not null)
                    merchantCompany.Type = CompanyService.Common.Enums.CompanyTypeEnum.Supplier;

                return companyModels;
            });

        service.GetCompanyByIdAsync(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(arg =>
            {
                var id = arg.Arg<string>();
                var company = new CompanyModel
                {
                    Id = id,
                    BankAccounts = [Guid.NewGuid().ToString()],
                    MerchantAutomaticPullAllowed = !id.Equals(Configuration.ForbiddenAutomaticRollbacksMerchantId),
                };

                if (company.Id == Configuration.CustomerCompanyIdWithValidBankAccount)
                    company.BankAccounts = [Configuration.ValidBankAccountId];

                if (company.Id == Configuration.ForbiddenAutomaticRollbacksMerchantId)
                    company.Type = CompanyService.Common.Enums.CompanyTypeEnum.Supplier;

                return company;
            });

        service.GetCompanyPaymentDetailsByIdAsync(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(arg =>
            {
                var id = arg.Arg<string>();
                var company = new CompanyPaymentDetailsModel
                {
                    BankAccounts = [Guid.NewGuid().ToString()],
                    MerchantAutomaticPullAllowed = !id.Equals(Configuration.ForbiddenAutomaticRollbacksMerchantId),
                    PublicIdentifier = "123",
                    BlueTapeCompanyId = Configuration.MerchantCompanyId,
                };

                return company;
            });

        return service;
    }

    public static IAionHttpClient MockAionHttpClient(bool noEnoughBalance)
    {
        var service = Substitute.For<IAionHttpClient>();

        service.CreateAchPush(Arg.Any<CreateAchModel>(), Arg.Any<Guid>(), Arg.Any<PaymentSubscriptionType>(), Arg.Any<CancellationToken>())
            .Returns(arg =>
            {
                var modelArg = arg.Arg<CreateAchModel>();
                if (Math.Abs(modelArg.Amount - (decimal)Configuration.HttpClientExceptionAmount) < Tolerance)
                    throw new HttpClientRequestException($"CreateAchPull failed at path", null, string.Empty);

                if (Math.Abs(modelArg.Amount - (decimal)Configuration.AionExceptionAmount) < Tolerance)
                    throw new AionResponseException();

                if (Math.Abs(modelArg.Amount - (decimal)Configuration.TooManyRequestsAmount) < Tolerance)
                    throw new TooManyRequestsException();

                if (Math.Abs(modelArg.Amount - (decimal)Configuration.InternalExceptionAmount) < Tolerance)
                    throw new Exception();

                return new CreateAchResponseModel { Result = true, AchObj = new() };
            });

        service.CreateAchPull(Arg.Any<CreateAchModel>(), Arg.Any<Guid>(), Arg.Any<PaymentSubscriptionType>(), Arg.Any<CancellationToken>())
            .Returns(arg =>
            {
                var modelArg = arg.Arg<CreateAchModel>();
                if (Math.Abs(modelArg.Amount - (decimal)Configuration.HttpClientExceptionAmount) < Tolerance)
                    throw new HttpClientRequestException($"CreateAchPull failed at path", null, string.Empty);

                if (Math.Abs(modelArg.Amount - (decimal)Configuration.AionExceptionAmount) < Tolerance)
                    throw new AionResponseException();

                if (Math.Abs(modelArg.Amount - (decimal)Configuration.TooManyRequestsAmount) < Tolerance)
                    throw new TooManyRequestsException();

                if (Math.Abs(modelArg.Amount - (decimal)Configuration.InternalExceptionAmount) < Tolerance)
                    throw new Exception();

                return new CreateAchResponseModel { Result = true, AchObj = new() };
            });

        service.CreateInternalTransfer(Arg.Any<CreateInternalModel>(), Arg.Any<Guid>(), Arg.Any<PaymentSubscriptionType>(), Arg.Any<CancellationToken>())
            .Returns(arg =>
            {
                var modelArg = arg.Arg<CreateInternalModel>();
                if (Math.Abs(modelArg.Amount - (decimal)Configuration.HttpClientExceptionAmount) < Tolerance)
                    throw new HttpClientRequestException($"CreateAchPull failed at path", null, string.Empty);

                if (Math.Abs(modelArg.Amount - (decimal)Configuration.AionExceptionAmount) < Tolerance)
                    throw new AionResponseException();

                if (Math.Abs(modelArg.Amount - (decimal)Configuration.TooManyRequestsAmount) < Tolerance)
                    throw new TooManyRequestsException();

                if (Math.Abs(modelArg.Amount - (decimal)Configuration.InternalExceptionAmount) < Tolerance)
                    throw new Exception();

                var model = new BookTransferObj()
                {
                    Id = modelArg.TransactionNumber,
                };
                return model;
            });

        service.CreateExternalTransfer(
                Arg.Any<CreateAchModel>(),
                Arg.Any<Guid>(),
                Arg.Any<PaymentSubscriptionType>(),
                Arg.Any<TransactionType>(),
                Arg.Any<AionPaymentMethodType>(),
                Arg.Any<CancellationToken>())
            .Returns(arg =>
            {
                var modelArg = arg.Arg<CreateAchModel>();
                if (Math.Abs(modelArg.Amount - (decimal)Configuration.HttpClientExceptionAmount) < Tolerance)
                    throw new HttpClientRequestException($"CreateAchPull failed at path", null, string.Empty);

                if (Math.Abs(modelArg.Amount - (decimal)Configuration.AionExceptionAmount) < Tolerance)
                    throw new AionResponseException();

                if (Math.Abs(modelArg.Amount - (decimal)Configuration.TooManyRequestsAmount) < Tolerance)
                    throw new TooManyRequestsException();

                if (Math.Abs(modelArg.Amount - (decimal)Configuration.InternalExceptionAmount) < Tolerance)
                    throw new Exception();

                var model = new BlueTapeTransactionResponseModel()
                {
                    AionReferenceId = modelArg.TransactionNumber,
                };
                return model;
            });

        service.CreateInternalTransferV2(
                Arg.Any<CreateInternalModel>(),
                Arg.Any<Guid>(),
                Arg.Any<PaymentSubscriptionType>(),
                Arg.Any<CancellationToken>())
            .Returns(arg =>
            {
                var modelArg = arg.Arg<CreateInternalModel>();
                if (Math.Abs(modelArg.Amount - (decimal)Configuration.HttpClientExceptionAmount) < Tolerance)
                    throw new HttpClientRequestException($"CreateAchPull failed at path", null, string.Empty);

                if (Math.Abs(modelArg.Amount - (decimal)Configuration.AionExceptionAmount) < Tolerance)
                    throw new AionResponseException();

                if (Math.Abs(modelArg.Amount - (decimal)Configuration.TooManyRequestsAmount) < Tolerance)
                    throw new TooManyRequestsException();

                if (Math.Abs(modelArg.Amount - (decimal)Configuration.InternalExceptionAmount) < Tolerance)
                    throw new Exception();

                var model = new BlueTapeTransactionResponseModel()
                {
                    AionReferenceId = modelArg.TransactionNumber,
                };
                return model;
            });

        service.GetAccountBalance(Arg.Any<AccountCodeType>(), Arg.Any<string>(), Arg.Any<PaymentSubscriptionType>(), Arg.Any<CancellationToken>())
            .Returns(arg => noEnoughBalance ? 0 : (decimal)Configuration.DefaultBalanceForAion);

        return service;
    }

    public static IInvoiceHttpClient MockInvoiceHttpClient()
    {
        var service = Substitute.For<IInvoiceHttpClient>();

        service.GetInvoicesByIdsAsync(Arg.Any<string[]>(), Arg.Any<CancellationToken>())
            .Returns(arg =>
            {
                var invoiceIds = arg.Arg<string[]>();
                var isRollbackForbidden = invoiceIds.Contains(Configuration.InvoiceIdForbiddenAutomaticRollbacks1) ||
                    invoiceIds.Contains(Configuration.InvoiceIdForbiddenAutomaticRollbacks2);

                var amount = PaymentRequestData.CreateValidInvoicePaymentMessage().PaymentRequestDetails.PayablesDetails.First().PayableAmount;
                var models = arg.Arg<string[]>().Select(id => new InvoiceModel
                {
                    Id = id,
                    TotalAmount = (decimal)amount,
                    Status = InvoiceConstants.PlacedStatus,
                    CompanyId = isRollbackForbidden ? Configuration.ForbiddenAutomaticRollbacksMerchantId : Configuration.MerchantCompanyId,
                    CustomerAccountId = Configuration.CustomerAccountId,
                    PaymentDetails = new PaymentDetailsModel
                    {
                        PaymentType = PaymentType.Factoring
                    }
                }).ToList();

                return models;
            });

        return service;
    }

    public static ILinqpalHttpClient MockNodeJsHttpClient()
    {
        var service = Substitute.For<ILinqpalHttpClient>();

        service.SendTransactionIsRecalled(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(Task.CompletedTask);

        return service;
    }

    public static ILoanServiceHttpClient MockLoanServiceHttpClient()
    {
        var service = Substitute.For<ILoanServiceHttpClient>();

        service.Get<object>(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(new object());

        service.Post<object, object>(Arg.Any<string>(), Arg.Any<object>(), Arg.Any<CancellationToken>())
            .Returns(new object());

        return service;
    }

    public static IKeyVaultService MockKeyVaultService()
    {
        var service = Substitute.For<IKeyVaultService>();

        service.GetSecret(Arg.Any<string>())
            .Returns(arg =>
            {
                var key = arg.Arg<string>();
                if (key == KeyVaultKeysConstants.AionRollBaсkErrorCodes) return JsonSerializer.Serialize(AionRollBakErrorCodes);

                return key;
            });

        return service;
    }

    public static IPaymentRequestValidator MockPaymentRequestValidator()
    {
        var service = Substitute.For<IPaymentRequestValidator>();

        service.ValidateInvoicePaymentRequest(
                Arg.Any<CreatePaymentRequestModel>(),
                Arg.Any<List<InvoiceModel>>(),
                Arg.Any<List<PaymentRequestPayableModel>>(),
                Arg.Any<LoanDto>(),
                Arg.Any<CancellationToken>())
            .Returns(Task.CompletedTask);

        return service;
    }

    public static ILoanExternalService MockLoanHttpExternalService()
    {
        var service = Substitute.For<ILoanExternalService>();

        service.ChangePaymentStatus(Arg.Any<Guid>(), Arg.Any<UpdatePaymentDto>(), Arg.Any<CancellationToken>())
            .Returns(new ChangePaymentResultDto());

        return service;
    }

    public static ILoanManagementService MockLoanManagementServiceService()
    {
        var service = Substitute.For<ILoanManagementService>();

        service.FindLoans(Arg.Any<LoanQuery>(), Arg.Any<CancellationToken>())
            .Returns(new List<LoanDto>
            {
                new()
                {
                    Id = Configuration.TestDrawId,
                    LoanDetails = new LoanDetailsDto
                    {
                        LoanOutstandingAmount = Configuration.LoanOutstandingAmount
                    },
                    CreatedAt = DateTime.Now
                }
            });

        service.FindLoan(Arg.Any<LoanQuery>(), Arg.Any<CancellationToken>())
            .Returns(new LoanDto
            {
                Id = Configuration.TestDrawId,
                LoanDetails = new LoanDetailsDto
                {
                    LoanOutstandingAmount = Configuration.LoanOutstandingAmount
                },
                CreatedAt = DateTime.Now
            });

        service.GetLoanById(Arg.Any<Guid>(), Arg.Any<CancellationToken>())
            .Returns(new LoanDto
            {
                Id = Configuration.TestDrawId,
                LoanDetails = new LoanDetailsDto
                {
                    LoanOutstandingAmount = Configuration.LoanOutstandingAmount
                },
                CreatedAt = DateTime.Now
            });

        return service;
    }

    public static ILmsExternalService MockLmsExternalService()
    {
        var service = Substitute.For<ILmsExternalService>();

        var loan = new LoanDto()
        {
            LoanDetails = new LoanDetailsDto()
            {
                LoanOutstandingAmount = 100000,
            },

        };
        service.GetLoanDetailedById(Arg.Any<Guid>(), Arg.Any<CancellationToken>())
            .Returns(loan);

        service.FindLoans(Arg.Any<LoanQuery>(), Arg.Any<CancellationToken>())
            .Returns(new List<LoanDto>
                {
                    new()
                    {
                        Id = Guid.NewGuid(),
                        LoanDetails = new LoanDetailsDto
                        {
                             LoanOutstandingAmount = Configuration.LoanOutstandingAmount
                        }
                    }
                });

        return service;
    }

    public static IConfiguration MockConfiguration()
    {
        const string url = "https://api-dev.bluetape.com.";

        var service = Substitute.For<IConfiguration>();

        service[Arg.Any<string>()]
            .Returns(arg =>
            {
                var key = arg.Arg<string>();
                if (key == ConfigurationKeys.PaymentApiKey) return ConfigurationKeys.ApiKeyName;

                if (key == KeyVaultKeysConstants.AionRollBaсkErrorCodes) return JsonSerializer.Serialize(AionRollBakErrorCodes);

                if (key == AionClientConstants.AionServiceUrl) return JsonSerializer.Serialize(url);

                if (key == AzureSecrets.LoanServiceUrl) return JsonSerializer.Serialize(url);
                if (key == AzureSecrets.LoanServiceApiKey) return ConfigurationKeys.ApiKeyName;

                if (key == InvoiceClientConstants.InvoiceServiceUrl) return JsonSerializer.Serialize(url);
                if (key == InvoiceClientConstants.LocalInvoiceServiceUrl) return JsonSerializer.Serialize(url);

                if (key == CompanyClientConstants.CompanyServiceUrl) return JsonSerializer.Serialize(url);

                if (key == ConfigConstants.AionAchPullHoldDays) return JsonSerializer.Serialize(0);
                if (key == ConfigConstants.AionAchPushHoldDays) return JsonSerializer.Serialize(1);

                return url;
            });

        return service;
    }

    public static ISnsEndpoint MockSnsEndpoint()
    {
        var service = Substitute.For<ISnsEndpoint>();

        service.PublishSlackNotificationAsync(
            Arg.Any<EventMessageBody>(),
            Arg.Any<string>(),
            Arg.Any<string>(),
            Arg.Any<CancellationToken>())
            .Returns(new PublishResponse());

        return service;
    }

    public static IPaymentCompatibilityService MockPaymentCompatibilityService()
    {
        var service = Substitute.For<IPaymentCompatibilityService>();

        return service;
    }

    public static ISequencesRepository MockSequencesRepository()
    {
        var service = Substitute.For<ISequencesRepository>();

        service.GetNextTransactionIdentifierSequenceNumber(Arg.Any<CancellationToken>())
            .Returns(Task.FromResult(123321L));

        return service;
    }

    public static ICustomerRepository MockCustomerRepository()
    {
        var service = Substitute.For<ICustomerRepository>();

        service.GetByIdAsync(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(new CustomerEntity()
            {
                Phone = "PHONE",
                EmailAddress = "<EMAIL>",
                Settings = new CustomerSettingsEntity()
                {
                    InHouseCredit = new InHouseCreditEntity()
                    {
                        IsEnabled = true,
                    }
                }
            });

        return service;
    }

    public static IUserRepository MockUserRepository()
    {
        var service = Substitute.For<IUserRepository>();

        service.GetByLoginInformationAsync(Arg.Any<IEnumerable<string>>(), Arg.Any<CancellationToken>())
            .Returns([new() { ExternalId = "ExternalId" }]);

        return service;
    }

    public static IUserRoleRepository MockUserRoleRepository()
    {
        var service = Substitute.For<IUserRoleRepository>();

        service.GetByExternalIds(Arg.Any<IEnumerable<string>>(), Arg.Any<CancellationToken>())
            .Returns([new() { CompanyId = Configuration.CustomerCompanyIdWithValidBankAccount }]);

        return service;
    }

    public static IOperationsService MockDatabaseOperationsService()
    {
        var service = Substitute.For<IOperationsService>();

        var merchantFee = 1;
        service.GetMerchantFeePerInvoice(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(merchantFee);

        var processedAmount = 10;
        service.GetProcessedAmountPerInvoice(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(processedAmount);

        return service;
    }

    public static IConnectorMessageSender MockConnectorMessageSender()
    {
        var messageSender = Substitute.For<IConnectorMessageSender>();

        messageSender.SendMessage(Arg.Any<ConnectorMessagePayload>(), Arg.Any<CancellationToken>())
            .Returns(Task.CompletedTask);

        return messageSender;
    }

    public static IInvoiceRepository MockInvoiceRepository()
    {
        var repository = Substitute.For<IInvoiceRepository>();

        repository.GetById(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(callInfo =>
            {
                var id = callInfo.Arg<string>();
                return new DataAccess.Mongo.Entities.Invoice.InvoiceEntity
                {
                    CompanyId = "CompanyId",
                    PayerId = "PayerId",
                    TakenById = "TakenById",
                    CustomerAccountId = "CustomerAccountId",
                    Id = id
                };
            });

        repository.GetByIds(Arg.Any<IEnumerable<string>>(), Arg.Any<CancellationToken>())
            .Returns(callInfo =>
            {
                var ids = callInfo.Arg<IEnumerable<string>>();
                return ids.Select(id => new DataAccess.Mongo.Entities.Invoice.InvoiceEntity
                {
                    CompanyId = "CompanyId",
                    PayerId = "PayerId",
                    TakenById = "TakenById",
                    CustomerAccountId = "CustomerAccountId",
                    Id = id
                }).ToList();
            });

        return repository;
    }

    public static IFinalPaymentOperationSyncService MockOperationSyncService()
    {
        var service = Substitute.For<IFinalPaymentOperationSyncService>();

        service.HandleRetry(Arg.Any<PaymentRequestEntity>(), Arg.Any<CancellationToken>())
            .Returns(Task.CompletedTask);

        return service;
    }

    public static IDrawApprovalRepository MockDrawApprovalRepository()
    {
        var repository = Substitute.For<IDrawApprovalRepository>();

        repository.GetByInvoicesIds(Arg.Any<string[]>(), Arg.Any<CancellationToken>())
            .Returns(new DrawApprovalEntity());

        return repository;
    }

    public static ILoanExternalService MockLoanExternalService()
    {
        var repository = Substitute.For<ILoanExternalService>();

        repository.GetLoansByDrawApprovalId(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(new List<LoanDto>() { new LoanDto() });

        repository.CreatePayment(Arg.Any<CreatePaymentDto>(), Arg.Any<CancellationToken>())
            .Returns(new PaymentDto());

        repository.ChangePaymentStatus(Arg.Any<Guid>(), Arg.Any<UpdatePaymentDto>(), Arg.Any<CancellationToken>())
            .Returns(new ChangePaymentResultDto());

        return repository;
    }

    public static IOperationsRepository MockOperationRepository()
    {
        var repository = Substitute.For<IOperationsRepository>();

        repository.GetByOwnerIds(Arg.Any<IEnumerable<string>>(), Arg.Any<List<string>>(), Arg.Any<CancellationToken>())
            .Returns(new List<OperationEntity>());

        return repository;
    }

    public static ISendGridBtClient MockSendGridBtClient()
    {
        var client = Substitute.For<ISendGridBtClient>();

        client.SendTemplatedEmailAsync(Arg.Any<EmailGridBtMessageModel<object>>(), Arg.Any<CancellationToken>())
            .Returns(Task.CompletedTask);

        return client;
    }

    public static ISendGridClient MockSendGridClient()
    {
        var client = Substitute.For<ISendGridClient>();

        var fakeResponse = new HttpResponseMessage();
        var fakeResponseHeader = fakeResponse.Headers;
        fakeResponseHeader.Add("X-Message-Id", "123xyz");

        client.SendEmailAsync(Arg.Any<SendGridMessage>(), Arg.Any<CancellationToken>())
            .Returns(new Response(HttpStatusCode.OK, new StringContent("Test content"), fakeResponseHeader));

        return client;
    }

    public static INotificationReceiversService MockNotificationReceiversService()
    {
        var service = Substitute.For<INotificationReceiversService>();

        service.GetSupplierNotificationReceivers(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(["<EMAIL>", "<EMAIL>"]);

        service.GetCompanyNotificationReceivers(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(["<EMAIL>"]);

        return service;
    }

    public static INotificationsRepository MockNotificationsRepository()
    {
        var repository = Substitute.For<INotificationsRepository>();

        repository.Create(Arg.Any<NotificationEntity>(), Arg.Any<CancellationToken>())
            .Returns(Task.CompletedTask);

        repository.Update(Arg.Any<NotificationEntity>(), Arg.Any<CancellationToken>())
            .Returns(new NotificationEntity());

        repository.GetByPredicate(Arg.Any<Expression<Func<NotificationEntity, bool>>>())
            .Returns(new List<NotificationEntity>() { new() });

        return repository;
    }

    public static ILoanReportingService MockLoanReportingService()
    {
        var repository = Substitute.For<ILoanReportingService>();

        repository.GetLoansByDrawApprovalIdsAsync(Arg.Any<IEnumerable<string>>(), Arg.Any<CancellationToken>())
            .Returns([new LoanDto() { Id = Guid.NewGuid() }]);

        return repository;
    }

    public static ILoanApplicationRepository MockLoanApplicationRepository()
    {
        var repository = Substitute.For<ILoanApplicationRepository>();

        repository.GetByLmsId(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(new LoanApplicationEntity());

        return repository;
    }

    public static ICreditApplicationAuthorizationDetailsRepository MockCreditApplicationAuthorizationDetailsRepository()
    {
        var repository = Substitute.For<ICreditApplicationAuthorizationDetailsRepository>();

        repository.GetByCreditApplicationId(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(new List<CreditApplicationAuthorizationDetailsDocument>());

        return repository;
    }

    public static ILoanPricingPackageRepository MockLoanPricingPackageRepository()
    {
        var repository = Substitute.For<ILoanPricingPackageRepository>();

        repository.GetByIds(Arg.Any<IEnumerable<string>>(), Arg.Any<CancellationToken>())
            .Returns(new List<LoanPricingPackageEntity>());

        return repository;
    }

    public static IDrawRepaymentManualMessageSender MockDrawRepaymentManualMessageSender()
    {
        var repository = Substitute.For<IDrawRepaymentManualMessageSender>();

        repository.SendMessage(Arg.Any<ServiceBusMessageBt<DrawRepaymentManualRequestMessage>>(), Arg.Any<CancellationToken>())
            .Returns(Task.CompletedTask);

        return repository;
    }

    public static IManualPaymentPullMessageSender MockManualPaymentPullMessageSender()
    {
        var repository = Substitute.For<IManualPaymentPullMessageSender>();

        repository.SendMessage(Arg.Any<ServiceBusMessageBt<ManualPaymentPullRequestMessage>>(), Arg.Any<CancellationToken>())
            .Returns(Task.CompletedTask);

        return repository;
    }
}
