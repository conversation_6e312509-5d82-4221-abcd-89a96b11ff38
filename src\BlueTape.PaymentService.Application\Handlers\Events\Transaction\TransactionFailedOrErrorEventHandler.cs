﻿using BlueTape.PaymentService.Application.Abstractions.Senders;
using BlueTape.PaymentService.Application.Models.Events.Transaction;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Messages;
using BlueTape.ServiceBusMessaging.Attributes;
using MediatR;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.Application.Handlers.Events.Transaction;

public class TransactionFailedOrErrorEventHandler(
    IPaymentTransactionRepository paymentTransactionRepository,
    INotificationMessageSender notificationMessageSender,
    IPaymentTransactionHistoryRepository paymentTransactionHistoryRepository,
    ILogger<TransactionFailedOrErrorEventHandler> logger) :
    BaseTransactionEventHandler(paymentTransactionRepository, paymentTransactionHistoryRepository, logger),
    IRequestHandler<TransactionFailedOrErrorEvent>
{
    protected override IEnumerable<TransactionStatus> AllowedTransactionStatus => new[]
    {
        TransactionStatus.Failed,
        TransactionStatus.Error
    };

    public Task Handle(TransactionFailedOrErrorEvent request, CancellationToken cancellationToken)
        => base.Handle(request, cancellationToken);

    protected override async Task HandleAch(PaymentTransactionEntity transaction, CancellationToken ct)
    {
        switch (transaction.PaymentRequest.FlowTemplateCode)
        {
            case DomainConstants.DrawRepaymentCard:
            case DomainConstants.DrawRepayment:
                await HandleAchDrawRepayment(transaction, ct);
                break;
            case DomainConstants.InvoicePaymentCard:
            case DomainConstants.InvoicePayment:
            case DomainConstants.InvoicePaymentV2:
            case DomainConstants.InvoiceDisbursementV2:
                await HandleAchPayNow(transaction, ct);
                break;
            case DomainConstants.FinalPayment:
            case DomainConstants.FinalPaymentV2:
            case DomainConstants.FactoringFinalPayment:
            case DomainConstants.FactoringDisbursement:
            case DomainConstants.DrawDisbursement:
                await SendBusNotification(transaction, ct);
                break;
            default:
                logger.LogWarning($"Unable to handle payment request of type: {transaction.PaymentRequest.FlowTemplateCode}, paymentRequestId: {transaction.PaymentRequestId}");
                break;
        }
    }

    private Task HandleAchPayNow(PaymentTransactionEntity transaction, CancellationToken ct)
        => SendBusNotification(transaction, ct);

    private Task HandleAchDrawRepayment(PaymentTransactionEntity transaction, CancellationToken ct)
        => SendBusNotification(transaction, ct);

    private async Task SendBusNotification(PaymentTransactionEntity transaction, CancellationToken ct)
    {
        var transactionHistory = await GetTransactionHistory(transaction.Id, ct);

        var messagesToSend = new ServiceBusMessageBt<NotificationMessagePayloadV2>(new NotificationMessagePayloadV2
        {
            Id = transactionHistory!.Id,
            NotificationType = NotificationType.TransactionHistoryUpdate
        });

        await notificationMessageSender.SendMessage(messagesToSend, ct);
    }
}