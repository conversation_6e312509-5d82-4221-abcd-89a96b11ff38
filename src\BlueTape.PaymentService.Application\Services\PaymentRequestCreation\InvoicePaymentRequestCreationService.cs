﻿using AutoMapper;
using BlueTape.InvoiceClient.Abstractions;
using BlueTape.InvoiceService.Models.Invoice;
using BlueTape.LS.DTOs.Loan;
using BlueTape.PaymentService.Application.Abstractions.Senders;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Abstractions.Services.External.Ledger;
using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;
using BlueTape.PaymentService.Application.Abstractions.Validators;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Messages;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.InvoicePayment;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Abstractions;
using BlueTape.PaymentService.UnitOfWork.Abstractions;
using BlueTape.ServiceBusMessaging.Attributes;
using Microsoft.Extensions.Logging;
using TinyHelpers.Extensions;

namespace BlueTape.PaymentService.Application.Services.PaymentRequestCreation;

public class InvoicePaymentRequestCreationService(
    IMapper mapper,
    IUnitOfWork unitOfWork,
    IPaymentFlowTemplatesEngine templatesEngine,
    IInvoiceHttpClient invoiceHttpClient,
    IPaymentRequestValidator paymentRequestValidator,
    ILogger<InvoicePaymentRequestCreationService> logger,
    IOperationSyncMessageSender operationSyncMessageSender,
    IPaymentTransactionHistoryRepository paymentTransactionHistoryRepository,
    ILedgerService ledgerService,
    INotificationMessageSender notificationMessageSender,
    IPaymentRequestPayableService paymentRequestPayableService,
    ILoanManagementService loanManagementService) : BasePaymentRequestCreationService(
    mapper, unitOfWork, templatesEngine, invoiceHttpClient, logger, operationSyncMessageSender, paymentRequestPayableService, loanManagementService), IInvoicePaymentRequestCreationService
{
    public Task<PaymentRequestModel> Add(InvoicePaymentRequestMessage paymentRequestMessage, string createdBy, CancellationToken ct)
    {
        using (logger.BeginScope(new Dictionary<string, object>
               {
                   { "FlowTemplateCode", paymentRequestMessage.FlowTemplateCode },
                   { "SellerCompanyId", paymentRequestMessage.PaymentRequestDetails.SellerDetails.CompanyId },
                   { "CustomerCompanyId", paymentRequestMessage.PaymentRequestDetails.CustomerDetails.Id },
                   { "PaymentMethod", paymentRequestMessage.PaymentRequestDetails.PaymentMethod },
               }))
        {
            logger.LogInformation("Create payment request started. Payload: {@paymentRequestMessage}", paymentRequestMessage);

            var createPaymentRequest = mapper.Map<CreatePaymentRequestModel>(paymentRequestMessage);
            var discountDetails = paymentRequestMessage.PaymentRequestDetails.DiscountDetails;
            if (!discountDetails.IsEmpty())
            {
                var createDiscountDetails = discountDetails.Select(x => new CreatePaymentRequestFeeModel()
                {
                    Amount = x.Amount,
                    CompanyId = x.CompanyId!,
                    Type = Enum.Parse<FeeType>(x.Type?.FirstCharToUpper()!)
                });
                createPaymentRequest.PaymentRequestFees.AddRange(createDiscountDetails);
            }

            return Add(createPaymentRequest, createdBy, ct);
        }
    }

    protected override async Task SendNotification(CreatePaymentRequestModel createPaymentRequest, PaymentRequestEntity paymentRequestEntity, IEnumerable<InvoiceModel> existingInvoices, CancellationToken ct)
    {
        await ledgerService.HandlePaymentRequestCreated(paymentRequestEntity.Id, createPaymentRequest, ct);

        var messagesToSend = new ServiceBusMessageBt<NotificationMessagePayloadV2>(new NotificationMessagePayloadV2
        {
            Id = paymentRequestEntity.Id,
            NotificationType = NotificationType.PaymentRequestCreated
        });

        await notificationMessageSender.SendMessage(messagesToSend, ct);
    }

    protected override Task ValidatePaymentRequest(CreatePaymentRequestModel createPaymentRequest,
        List<InvoiceModel> existingInvoices,
        List<PaymentRequestPayableModel> processedPayables, LoanDto? loan, CancellationToken ct)
    {
        return paymentRequestValidator.ValidateInvoicePaymentRequest(createPaymentRequest, existingInvoices, processedPayables, loan, ct);
    }
}
