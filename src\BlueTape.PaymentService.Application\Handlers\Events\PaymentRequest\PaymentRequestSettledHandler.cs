using BlueTape.PaymentService.Application.Abstractions.Senders;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Models.Events.PaymentRequest;
using BlueTape.PaymentService.DataAccess.External.Abstractions.Services;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Invoice;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Messages;
using BlueTape.PaymentService.UnitOfWork.Abstractions;
using MediatR;
using Microsoft.Extensions.Logging;
using TinyHelpers.Extensions;

namespace BlueTape.PaymentService.Application.Handlers.Events.PaymentRequest;

public class PaymentRequestSettledHandler(
    IUnitOfWork unitOfWork,
    IOperationsService operationsService,
    IInvoiceRepository invoiceRepository,
    ILoanApplicationRepository loanApplicationRepository,
    ILoanPricingPackageRepository loanPricingPackageRepository,
    IConnectorMessageSender connectorMessageSender,
    ILogger<PaymentRequestSettledHandler> logger,
    ILoanManagementService loanManagementService,
    ILmsExternalService lmsExternalService) : BasePaymentRequestHandler(unitOfWork, logger),
    IRequestHandler<PaymentRequestSettledEvent>
{
    protected override IEnumerable<PaymentRequestStatus> AllowedPaymentRequestStatuses =>
    [
        PaymentRequestStatus.Settled
    ];

    public Task Handle(PaymentRequestSettledEvent request, CancellationToken cancellationToken)
        => base.Handle(request, cancellationToken);

    protected sealed override async Task HandleAch(PaymentRequestEntity paymentRequest, CancellationToken ct)
    {
        switch (paymentRequest.FlowTemplateCode)
        {
            case DomainConstants.DrawRepaymentCard:
            case DomainConstants.DrawRepayment:
            case DomainConstants.DrawRepaymentManual:
                await HandleAchDrawRepayment(paymentRequest, ct);
                break;
            case DomainConstants.InvoicePaymentCard:
            case DomainConstants.InvoicePayment:
                await HandleAchPayNow(paymentRequest, ct);
                break;
            case DomainConstants.InvoicePaymentV2:
                await HandleInvoicePaymentV2(paymentRequest, ct);
                break;
            case DomainConstants.DrawDisbursement:
            case DomainConstants.FinalPayment:
            case DomainConstants.FinalPaymentV2:
                await HandleDisbursementPayment(paymentRequest, ct);
                break;
            case DomainConstants.InvoiceDisbursementV2:
                break;
            case DomainConstants.FactoringDisbursement:
            case DomainConstants.FactoringFinalPayment:
                await HandleFactoringDisbursementPayment(paymentRequest, ct);
                break;
            default:
                logger.LogWarning($"Unable to handle payment request of type: {paymentRequest.FlowTemplateCode}, paymentRequestId: {paymentRequest.Id}");
                break;
        }
    }

    private async Task HandleAchPayNow(PaymentRequestEntity paymentRequest, CancellationToken ct)
    {
        var invoices = (await invoiceRepository.GetByIds(paymentRequest.PaymentRequestPayables.Select(x => x.PayableId), ct))
            .Where(x => x.Connector is not null)
            .ToList();

        if (!invoices.Any())
            return;

        // 2 possible invoice payment types: ach and card
        var paymentMethod = paymentRequest.RequestType == PaymentRequestType.InvoicePayment ? "ach" : "card";

        foreach (var payable in invoices)
        {
            var merchantFee = await operationsService.GetMerchantFeePerInvoice(payable.Id, ct);

            if (merchantFee is null)
            {
                logger.LogError("Unable to identify merchant fee is null, paymentRequestId: {paymentRequestId}", paymentRequest.Id);
                return;
            }

            await ProcessConnectorEvent(payable, merchantFee.Value, "final", paymentMethod, ct);
        }
    }

    private async Task HandleDisbursementPayment(PaymentRequestEntity paymentRequest, CancellationToken ct)
    {
        var invoices = (await invoiceRepository.GetByIds(paymentRequest.PaymentRequestPayables.Select(x => x.PayableId), ct))
            .Where(x => x.Connector is not null)
            .ToList();

        if (!invoices.Any())
            return;

        var loanApplication = await loanApplicationRepository.GetByLmsId(paymentRequest.PaymentRequestDetails!.DrawId.ToString(), ct);
        var advanceRate = loanApplication?.Metadata?.LoanPackage?.AdvanceRate;

        foreach (var payable in invoices)
        {
            decimal fee = 0;
            string status = "partial";

            if (advanceRate == 100 || paymentRequest.RequestType is PaymentRequestType.FinalPayment or PaymentRequestType.FinalPaymentV2)
            {
                status = "final";
                var feePercentage = loanApplication?.Metadata?.LoanPackage?.Merchant;
                if (payable.MaterialSubtotal != null)
                    fee = (decimal)payable.MaterialSubtotal.Value * feePercentage!.Value / 100;
            }

            await ProcessConnectorEvent(payable, fee, status, "BTC", ct);
        }
    }

    private async Task HandleFactoringDisbursementPayment(PaymentRequestEntity paymentRequest, CancellationToken ct)
    {
        var invoices = (await invoiceRepository.GetByIds(paymentRequest.PaymentRequestPayables.Select(x => x.PayableId), ct))
            .Where(x => x.Connector is not null)
            .ToList();

        if (!invoices.Any())
            return;

        var pricingPackagesId = invoices
            .Select(x => x.PaymentDetails?.PricingPackageId)
            .FirstOrDefault(x => !x.IsNullOrEmpty());

        var pricingPackage = pricingPackagesId != null
            ? (await loanPricingPackageRepository.GetByIds([pricingPackagesId], ct)).FirstOrDefault()
            : null;

        var advanceRate = pricingPackage?.Metadata?.AdvanceRate;

        foreach (var payable in invoices)
        {
            decimal fee = 0;
            string status = "partial";

            if (advanceRate == 100 || paymentRequest.RequestType is PaymentRequestType.FactoringFinalPayment)
            {
                status = "final";
                var feePercentage = pricingPackage?.Metadata?.Merchant;
                if (payable.MaterialSubtotal != null)
                    fee = (decimal)payable.MaterialSubtotal.Value * feePercentage!.Value / 100;
            }

            await ProcessConnectorEvent(payable, fee, status, "arAdvance", ct);
        }
    }

    private async Task HandleAchDrawRepayment(PaymentRequestEntity paymentRequest, CancellationToken ct)
    {
        var lmsPaymentId = paymentRequest.PaymentRequestDetails!.LMSPaymentId;
        await loanManagementService.ConfirmLmsPayment(paymentRequest.Id, lmsPaymentId, ct);
    }

    private async Task HandleInvoicePaymentV2(PaymentRequestEntity paymentRequest, CancellationToken ct)
    {
        var lmsPaymentId = paymentRequest.PaymentRequestDetails!.LMSPaymentId;
        await loanManagementService.ConfirmLmsPayment(paymentRequest.Id, lmsPaymentId, ct);
    }

    private async Task ProcessConnectorEvent(
        InvoiceEntity invoice,
        decimal fee,
        string status,
        string paymentMethod,
        CancellationToken ct)
    {
        var processedAmount = await operationsService.GetProcessedAmountPerInvoice(invoice.Id, ct);

        if (processedAmount is null)
        {
            logger.LogError("Unable to identify processed amount is null, invoice Id: {paymentRequestId}", invoice.Id);
            return;
        }

        var payload = new ConnectorMessagePayload()
        {
            Id = invoice.Id,
            Status = status,
            OperationType = OperationType.InvoicePaid,
            Amount = processedAmount.Value,
            Fee = fee,
            PaymentMethod = paymentMethod,
        };

        if (invoice.Connector is not null)
        {
            await connectorMessageSender.SendMessage(payload, ct);
        }
    }
}
