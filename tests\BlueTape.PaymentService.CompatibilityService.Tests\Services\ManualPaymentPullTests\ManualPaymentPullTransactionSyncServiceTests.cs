﻿using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.ManualPaymentPull;
using BlueTape.PaymentService.CompatibilityService.Services.ManualPaymentPull;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Operation;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Transaction;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using NSubstitute;
using Xunit;

namespace BlueTape.PaymentService.CompatibilityService.Tests.Services.ManualPaymentPullTests
{
    public class ManualPaymentPullTransactionSyncServiceTests
    {
        private readonly ITransactionsRepository _transactionsRepository;
        private readonly IManualPaymentPullCompatibilityMapper _compatibilityMapper;
        private readonly IManualPaymentPullTransactionSyncService _service;
        private readonly CancellationToken _cancellationToken;

        public ManualPaymentPullTransactionSyncServiceTests()
        {
            _transactionsRepository = Substitute.For<ITransactionsRepository>();
            _compatibilityMapper = Substitute.For<IManualPaymentPullCompatibilityMapper>();
            _service = new ManualPaymentPullTransactionSyncService(
                _transactionsRepository,
                _compatibilityMapper);
            _cancellationToken = CancellationToken.None;
        }

        [Fact]
        public async Task PerformTransactions_ShouldMapAndInsertTransactions()
        {
            // Arrange
            var paymentRequest = CreateSamplePaymentRequest();
            var operation = new OperationEntity { BlueTapeId = "op123" };
            var legacyTransactions = new List<TransactionEntity>
            {
                new TransactionEntity { OperationId = operation.BlueTapeId }
            };

            _compatibilityMapper.MapFromPaymentTransactionsToLegacyTransactions(
                paymentRequest, operation, _cancellationToken)
                .Returns(legacyTransactions);

            // Act
            await _service.PerformTransactions(paymentRequest, operation, _cancellationToken);

            // Assert
            await _transactionsRepository.Received(1).InsertMany(legacyTransactions, _cancellationToken);
        }

        private PaymentRequestEntity CreateSamplePaymentRequest()
        {
            return new PaymentRequestEntity
            {
                Id = Guid.NewGuid(),
                PayerId = "payer123",
                Amount = 100.50m,
                Date = DateOnly.FromDateTime(DateTime.UtcNow),
                PaymentMethod = PaymentMethod.Ach,
                Transactions = new List<PaymentTransactionEntity>
                {
                    new PaymentTransactionEntity
                    {
                        Id = Guid.Parse("00000000-0000-0000-0000-000000000001"),
                        Status = TransactionStatus.Cleared,
                        TransactionType = PaymentTransactionType.AchPull
                    },
                    new PaymentTransactionEntity
                    {
                        Id = Guid.Parse("00000000-0000-0000-0000-000000000002"),
                        Status = TransactionStatus.Processing,
                        TransactionType = PaymentTransactionType.AchPull
                    }
                }
            };
        }
    }
}