﻿using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.ManualPaymentPull;
using BlueTape.PaymentService.CompatibilityService.Extensions;
using BlueTape.PaymentService.CompatibilityService.Models;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Operation;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Transaction;
using BlueTape.PaymentService.Domain.Entities;
using TinyHelpers.Extensions;

namespace BlueTape.PaymentService.CompatibilityService.Services.ManualPaymentPull;

public class ManualPaymentPullTransactionSyncService(
    ITransactionsRepository transactionsRepository,
    IManualPaymentPullCompatibilityMapper compatibilityMapper) : IManualPaymentPullTransactionSyncService
{
    public async Task PerformTransactions(PaymentRequestEntity paymentRequest, OperationEntity operation,
        CancellationToken cancellationToken)
    {
        var legacyTransactions = await compatibilityMapper.MapFromPaymentTransactionsToLegacyTransactions(paymentRequest, operation, cancellationToken);
        await transactionsRepository.InsertMany(legacyTransactions, cancellationToken);
    }

    public async Task SyncTransactions(PaymentRequestEntity paymentRequest, CancellationToken cancellationToken)
    {
        var transactionsToSync = paymentRequest.GetTransactionsForSync();
        var syncTransactionModels = transactionsToSync.Select(compatibilityMapper.MapPaymentTransactionToSyncModel);
        var updateTransactionEntities = await MapSyncModelsToEntities(syncTransactionModels, cancellationToken);

        await updateTransactionEntities.ForEachAsync(async transactionUpdateEntity =>
            await transactionsRepository.Update(transactionUpdateEntity.PaymentTransactionId!,
                transactionUpdateEntity, cancellationToken), cancellationToken: cancellationToken);
    }

    private async Task<List<UpdateTransactionEntity>> MapSyncModelsToEntities(IEnumerable<SyncTransactionModel> syncTransactionModels, CancellationToken cancellationToken)
    {
        var updateTransactionEntities = new List<UpdateTransactionEntity>();
        await syncTransactionModels
            .ForEachAsync(async transactionToSync =>
            {
                if (!string.IsNullOrEmpty(transactionToSync.PaymentTransactionId))
                {
                    var entity = await compatibilityMapper.MapSyncModelToUpdateTransactionEntity(transactionToSync, cancellationToken);
                    updateTransactionEntities.Add(entity);
                }
            }, cancellationToken);

        return updateTransactionEntities;
    }
}
