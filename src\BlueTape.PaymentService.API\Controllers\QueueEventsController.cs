﻿using AutoMapper;
using BlueTape.PaymentService.API.ViewModels;
using BlueTape.PaymentService.Application.Abstractions.Processors;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Service;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Messages;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawDisbursement;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepaymentCard;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepaymentManual;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.FactoringDisbursement;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.FactoringFinalPayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.FinalPayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.InvoiceDisbursementV2;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.InvoicePayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.InvoicePaymentV2;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.ManualPaymentPull;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.SubscriptionFeePayment;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json;

namespace BlueTape.PaymentService.API.Controllers;

[ExcludeFromCodeCoverage(Justification = "Controllers are not required to be tested at the moment")]
[Authorize]
[ApiController]
[Route("[controller]")]
public class QueueEventsController(
    IInvoicePaymentRequestCreationService invoicePaymentRequestService,
    IInvoicePaymentV2RequestCreationService invoicePaymentV2RequestCreationService,
    IDrawRepaymentRequestCreationService drawRepaymentRequestService,
    IDrawRepaymentCardRequestCreationService drawRepaymentCardRequestService,
    IDrawRepaymentManualRequestCreationService drawRepaymentManualRequestService,
    IFinalPaymentRequestCreationService finalPaymentRequestService,
    IInvoiceDisbursementV2RequestCreationService invoiceDisbursementV2RequestCreationService,
    IFactoringFinalPaymentRequestCreationService factoringFinalPaymentRequestCreationService,
    IFactoringDisbursementRequestCreationService factoringDisbursementRequestCreationService,
    IDrawDisbursementRequestCreatorService drawDisbursementRequestCreatorService,
    IManualPaymentPullCreationService manualPaymentPullCreationService,
    ISubscriptionFeePaymentRequestCreationService subscriptionFeePaymentRequestCreationService,
    IUnifiedPaymentCreationService unifiedPaymentCreationService,
    IPaymentJobProcessor paymentJobProcessor,
    ITransactionStatusUpdateProcessor transactionStatusUpdateProcessor,
    ITransactionStatusUpdateService transactionStatusUpdateService,
    IPaymentCompatibilityService compatibilityService,
    ICommandManagementService commandManagementService,
    INotificationProcessor notificationProcessor,
    IMapper mapper) : ControllerBase
{
    /// <summary>
    /// Handles message from queue to create Pay Now payment request
    /// </summary>
    /// <returns></returns>
    [HttpPost("payment-request")]
    public async Task<PaymentRequestViewModel> HandlePaymentRequestMessage(JsonElement jsonElement,
        CancellationToken cancellationToken)
    {
        var baseMessage = JsonSerializer.Deserialize<BasePaymentRequestMessage>(jsonElement.GetRawText());

        switch (baseMessage?.FlowTemplateCode)
        {
            case DomainConstants.InvoicePaymentCard:
            case DomainConstants.InvoicePayment:
                return await AddPaymentRequestMessageV2<InvoicePaymentRequestMessage>(jsonElement, "queueController", cancellationToken);
            case DomainConstants.InvoicePaymentV2:
                return await AddPaymentRequestMessageV2<InvoicePaymentV2RequestMessage>(jsonElement, "queueController", cancellationToken);
            case DomainConstants.InvoiceDisbursementV2:
                return await AddPaymentRequestMessageV2<InvoiceDisbursementV2RequestMessage>(jsonElement, "queueController", cancellationToken);
            case DomainConstants.DrawRepayment:
                return await AddPaymentRequestMessageV2<DrawRepaymentRequestMessage>(jsonElement, "queueController", cancellationToken);
            case DomainConstants.DrawRepaymentCard:
                return await AddPaymentRequestMessageV2<DrawRepaymentCardRequestMessage>(jsonElement, "queueController", cancellationToken);
            case DomainConstants.DrawRepaymentManual:
                return await AddPaymentRequestMessageV2<DrawRepaymentManualRequestMessage>(jsonElement, "queueController", cancellationToken);
            case DomainConstants.FinalPayment:
            case DomainConstants.FinalPaymentV2:
                return await AddPaymentRequestMessageV2<FinalPaymentRequestMessage>(jsonElement, "queueController", cancellationToken);
            case DomainConstants.FactoringDisbursement:
                return await AddPaymentRequestMessageV2<FactoringDisbursementRequestMessage>(jsonElement, "queueController", cancellationToken);
            case DomainConstants.FactoringFinalPayment:
                return await AddPaymentRequestMessageV2<FactoringFinalPaymentRequestMessage>(jsonElement, "queueController", cancellationToken);
            case DomainConstants.DrawDisbursement:
                return await AddPaymentRequestMessageV2<DrawDisbursementRequestMessage>(jsonElement, "queueController", cancellationToken);
            case DomainConstants.ManualPaymentPull:
                return await AddPaymentRequestMessageV2<ManualPaymentPullRequestMessage>(jsonElement, "queueController", cancellationToken);
            case DomainConstants.SubscriptionFeePayment:
                return await AddPaymentRequestMessageV2<SubscriptionFeePaymentRequestMessage>(jsonElement, "queueController", cancellationToken);
            default:
                throw new ArgumentException("Unknown message type");
        }
    }

    private async Task<PaymentRequestViewModel> AddPaymentRequestMessageV2<T>(
        JsonElement jsonElement,
        string createdBy,
        CancellationToken cancellationToken) where T : BasePaymentRequestMessage
    {
        var paymentMessage = JsonSerializer.Deserialize<T>(jsonElement.GetRawText());
        if (paymentMessage == null) throw new ArgumentException("Impossible to process the message");

        var request = mapper.Map<CreatePaymentRequestModel>(paymentMessage);

        var paymentRequestModel = await unifiedPaymentCreationService.CreatePaymentRequest(request, createdBy, cancellationToken);
        return mapper.Map<PaymentRequestViewModel>(paymentRequestModel);
    }

    private async Task<PaymentRequestViewModel> AddPaymentRequestMessage<T>(
        Func<T, string, CancellationToken, Task<PaymentRequestModel>> addFunction,
        JsonElement jsonElement,
        CancellationToken cancellationToken) where T : BasePaymentRequestMessage
    {
        var invoicePaymentMessage = JsonSerializer.Deserialize<T>(jsonElement.GetRawText());
        if (invoicePaymentMessage == null) throw new ArgumentException("Impossible to process the message");

        var paymentRequestModel =
            await addFunction(invoicePaymentMessage, invoicePaymentMessage.CreatedBy, cancellationToken);
        return mapper.Map<PaymentRequestViewModel>(paymentRequestModel);
    }

    /// <summary>
    /// Triggers payment scheduled job
    /// </summary>
    /// <returns></returns>
    [HttpPost("payment-scheduled-job")]
    public Task TriggerPaymentScheduledJob(CancellationToken cancellationToken)
    {
        return paymentJobProcessor.Process(cancellationToken);
    }

    /// <summary>
    /// Triggers transaction status update scheduled job
    /// </summary>
    /// <returns></returns>
    [HttpGet("transaction-status-update-scheduled-job")]
    public async Task<IActionResult> GetById(CancellationToken ct)
    {
        await transactionStatusUpdateProcessor.Process(ct);
        return Ok();
    }

    /// <summary>
    /// Triggers notification consumer
    /// </summary>
    /// <returns></returns>
    [HttpPost("notification-consumer")]
    public Task TriggerNotificationConsumer(NotificationMessagePayloadV2 messagePayload, CancellationToken ctx)
    {
        return notificationProcessor.Execute(messagePayload, ctx);
    }

    /// <summary>
    /// Triggers transaction status update job
    /// </summary>
    /// <returns></returns>
    [HttpPost("transaction-status-update-job")]
    public Task TriggerTransactionStatusUpdateJob(TransactionStatusMessagePayload messagePayload, CancellationToken ctx)
    {
        return transactionStatusUpdateService.Process(messagePayload, "ApiRequest", ctx);
    }

    /// <summary>
    /// Triggers legacy operation sync in MongoDB by payment request id
    /// </summary>
    /// <returns></returns>
    [HttpPost("legacy/sync-operation/{id}")]
    public Task TriggerOperationSync([FromRoute] Guid id, CancellationToken ctx)
    {
        return compatibilityService.SyncOperation(id, ctx);
    }

    /// <summary>
    /// Performs legacy operation in MongoDB by payment request id
    /// </summary>
    /// <returns></returns>
    [HttpPost("legacy/perform-operation/{id}")]
    public Task TriggerPerformOperation([FromRoute] Guid id, CancellationToken ctx)
    {
        return compatibilityService.PerformOperation(id, ctx);
    }

    [HttpGet("command-event-processor/{commandId:Guid}")]
    public Task TestCommand([FromRoute] Guid commandId, CancellationToken cancellationToken)
    {
        return commandManagementService.ManageCommand(commandId, cancellationToken);
    }
}