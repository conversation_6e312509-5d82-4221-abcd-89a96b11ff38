﻿using AutoMapper;
using BlueTape.InvoiceClient.Abstractions;
using BlueTape.InvoiceService.Models.Invoice;
using BlueTape.PaymentService.Application.Abstractions.Senders;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Abstractions.Services.External.Ledger;
using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;
using BlueTape.PaymentService.Application.Abstractions.Validators;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.InvoiceDisbursementV2;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.InvoicePayment;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Abstractions;
using BlueTape.PaymentService.UnitOfWork.Abstractions;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.Application.Services.PaymentRequestCreation;
public class InvoiceDisbursementV2RequestCreationService(
    IMapper mapper,
    IUnitOfWork unitOfWork,
    IPaymentFlowTemplatesEngine templatesEngine,
    IInvoiceHttpClient invoiceHttpClient,
    IPaymentRequestValidator paymentRequestValidator,
    ILogger<InvoiceDisbursementV2RequestCreationService> logger,
    IOperationSyncMessageSender operationSyncMessageSender,
    IPaymentTransactionHistoryRepository paymentTransactionHistoryRepository,
    ILedgerService ledgerService,
    INotificationMessageSender notificationMessageSender,
    IPaymentRequestPayableService paymentRequestPayableService,
    ILoanManagementService loanManagementService) : InvoicePaymentRequestCreationService(
        mapper,
        unitOfWork,
        templatesEngine,
        invoiceHttpClient,
        paymentRequestValidator,
        logger,
        operationSyncMessageSender,
        paymentTransactionHistoryRepository,
        ledgerService,
        notificationMessageSender,
        paymentRequestPayableService,
        loanManagementService), IInvoiceDisbursementV2RequestCreationService
{
    public Task<PaymentRequestModel> Add(InvoiceDisbursementV2RequestMessage paymentRequestMessage, string createdBy, CancellationToken ct)
    {

        return Add((InvoicePaymentRequestMessage)paymentRequestMessage, createdBy, ct);
    }

    protected override Task SendNotification(CreatePaymentRequestModel createPaymentRequest, PaymentRequestEntity paymentRequestEntity, IEnumerable<InvoiceModel> existingInvoices, CancellationToken ct)
    {
        return Task.CompletedTask;
    }
}
