﻿
using AutoMapper;
using BlueTape.Common.ExceptionHandling.Models;
using BlueTape.Integrations.Aion.Accounts;
using BlueTape.PaymentService.API.Queries;
using BlueTape.PaymentService.API.ViewModels;
using BlueTape.PaymentService.API.ViewModels.Base;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Abstractions.Services.External;
using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Application.Models.CreatePaymentRequestMessageDtos;
using BlueTape.PaymentService.Domain.Entities.Filters;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json;

namespace BlueTape.PaymentService.API.Controllers;

[ExcludeFromCodeCoverage(Justification = "Controllers are not required to be tested at the moment")]
[Authorize]
[ApiController]
[Route("[controller]")]
public class PaymentRequestsController(
    IPaymentRequestService paymentRequestService,
    IInvoicePaymentRequestCreationService invoicePaymentRequestCreationService,
    IInvoicePaymentV2RequestCreationService invoicePaymentV2RequestCreationService,
    IDrawRepaymentRequestCreationService drawRepaymentRequestCreationService,
    IDrawRepaymentCardRequestCreationService drawRepaymentCardRequestCreationService,
    IDrawRepaymentManualRequestCreationService drawRepaymentManualRequestCreationService,
    IFinalPaymentRequestCreationService finalPaymentRequestCreationService,
    IFactoringFinalPaymentRequestCreationService factoringFinalPaymentRequestCreationService,
    IInvoiceDisbursementV2RequestCreationService invoiceDisbursementV2RequestCreationService,
    IFactoringDisbursementRequestCreationService factoringDisbursementRequestCreationService,
    IDrawDisbursementRequestCreatorService drawDisbursementRequestCreatorService,
    ISubscriptionFeePaymentRequestCreationService subscriptionFeePaymentRequestCreationService,
    IUnifiedPaymentCreationService unifiedPaymentCreationService,
    IMapper mapper,
    IPaymentTransactionService paymentTransactionService,
    IAionServiceV2 aionServiceV2,
    IPaymentRequestCommandService commandService) : ControllerBase
{
    /// <summary>
    /// Gets a single payment request full details
    /// </summary>
    /// <returns></returns>
    [ProducesResponseType(typeof(PaymentRequestViewModel), StatusCodes.Status200OK)]
    [HttpGet("{id}")]
    public async Task<PaymentRequestViewModel> GetById([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var paymentRequestModel = await paymentRequestService.GetById(id, cancellationToken);
        var result = mapper.Map<PaymentRequestViewModel>(paymentRequestModel);

        return result;
    }

    /// <summary>
    /// Cancels a payment request
    /// </summary>
    /// <returns></returns>
    [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorModel), StatusCodes.Status404NotFound)]
    [HttpDelete("{id}")]
    public Task CancelById([FromRoute] Guid id, [FromHeader, Required] string userId, CancellationToken cancellationToken)
    {
        return paymentRequestService.CancelPaymentRequest(id, userId, cancellationToken);
    }

    /// <summary>
    /// Cancels a payment request
    /// </summary>
    /// <returns></returns>
    [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorModel), StatusCodes.Status404NotFound)]
    [HttpDelete("draw/{id}")]
    public Task CancelByDrawId([FromRoute] Guid id, [FromHeader, Required] string userId, CancellationToken cancellationToken)
    {
        return paymentRequestService.CancelPaymentRequestByDrawId(id, userId, cancellationToken);
    }

    /// <summary>
    /// Lists payment requests by various filtering options
    /// </summary>
    /// <returns></returns>
    [ProducesResponseType(typeof(PaginatedResultViewModel<PaymentRequestViewModel>), StatusCodes.Status200OK)]
    [HttpGet]
    public async Task<PaginatedResultViewModel<PaymentRequestViewModel>> GetByFilter([FromQuery] PaymentRequestFilterQuery query, CancellationToken cancellationToken)
    {
        var filter = mapper.Map<PaymentRequestFilter>(query);
        var paymentRequestModel = await paymentRequestService.GetByFilter(filter, cancellationToken);
        var result = mapper.Map<PaginatedResultViewModel<PaymentRequestViewModel>>(paymentRequestModel);

        return result;
    }

    /// <summary>
    /// Create a payment request
    /// </summary>
    /// <returns></returns>
    [ProducesResponseType(typeof(void), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorModel), StatusCodes.Status400BadRequest)]
    [HttpPost]
    public async Task<PaymentRequestViewModel> Create(CreatePaymentRequestViewModel createPaymentRequest, [FromHeader] string createdBy, CancellationToken cancellationToken)
    {
        var createModel = mapper.Map<CreatePaymentRequestModel>(createPaymentRequest);
        var result = await unifiedPaymentCreationService.CreatePaymentRequest(createModel, createdBy, cancellationToken);
        return mapper.Map<PaymentRequestViewModel>(result);
    }

    [ProducesResponseType(typeof(void), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorModel), StatusCodes.Status400BadRequest)]
    [HttpPost("message")]
    public async Task<string> SendPaymentRequestMessage(CreatePaymentRequestDto createPaymentRequest, [FromHeader] string createdBy, CancellationToken cancellationToken)
    {
        var message = await paymentRequestService.SendManualInternalPaymentRequestMessage(createPaymentRequest, createdBy, cancellationToken);
        return JsonSerializer.Serialize(message);
    }

    [ProducesResponseType(typeof(void), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorModel), StatusCodes.Status400BadRequest)]
    [HttpPost("message/{messageType}")]
    public async Task<string> SendDrawManualPaymentRequestMessage(
        [FromBody] JsonElement rawRequest,
        [FromHeader] string createdBy,
        [FromRoute] PaymentRequestType messageType,
        CancellationToken cancellationToken)
    {
        var message = await paymentRequestService.SendManualPaymentPullRequestMessage(rawRequest, createdBy, messageType, cancellationToken);
        return JsonSerializer.Serialize(message);
    }

    /// <summary>
    /// Creates a transaction for payment request
    /// </summary>
    /// <returns></returns>
    [ProducesResponseType(typeof(void), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ErrorModel), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorModel), StatusCodes.Status404NotFound)]
    [HttpPost("{id}/transactions")]
    public Task CreateTransaction([FromRoute] Guid id, CreatePaymentRequestTransactionViewModel createTransactionRequest, [FromHeader] string createdBy, CancellationToken cancellationToken)
    {
        var createModel = mapper.Map<CreatePaymentRequestTransactionModel>(createTransactionRequest);
        return paymentTransactionService.Add(id, createModel, createdBy, cancellationToken);
    }

    /// <summary>
    /// Get commands
    /// </summary>
    /// <returns></returns>
    [ProducesResponseType(typeof(IEnumerable<PaymentRequestCommandViewModel>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorModel), StatusCodes.Status404NotFound)]
    [HttpGet("{id}/commands")]
    public async Task<IEnumerable<PaymentRequestCommandViewModel>> GetPaymentRequestCommand([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        var models = await commandService.GetByPaymentRequestId(id, cancellationToken);
        return mapper.Map<IEnumerable<PaymentRequestCommandViewModel>>(models);
    }

    /// <summary>
    /// Executes a transaction
    /// </summary>
    /// <returns></returns>
    [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorModel), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorModel), StatusCodes.Status404NotFound)]
    [HttpPost("{id}/transactions/{transactionId}/execute")]
    public Task ExecuteTransaction([FromRoute] Guid id, [FromRoute] Guid transactionId, [FromHeader] string updatedBy, CancellationToken cancellationToken)
    {
        return paymentTransactionService.Execute(id, transactionId, updatedBy, cancellationToken);
    }

    /// <summary>
    /// Checks whether a payment request exists for a payable
    /// </summary>
    /// <returns></returns>
    [ProducesResponseType(typeof(IEnumerable<PaymentRequestViewModel>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorModel), StatusCodes.Status404NotFound)]
    [HttpGet("/paymentRequests/payable/{id}/is-payment-request-exist")]
    public async Task<IEnumerable<PaymentRequestViewModel>> IsPaymentRequestExists([FromRoute] string id, CancellationToken cancellationToken)
    {
        var models = await paymentRequestService.GetByPayableId(id, cancellationToken);
        var result = mapper.Map<IEnumerable<PaymentRequestViewModel>>(models);

        return result;
    }

    /// <summary>
    /// Manually retry a failed transaction
    /// </summary>
    /// <returns></returns>
    [ProducesResponseType(typeof(PaymentTransactionViewModel), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorModel), StatusCodes.Status500InternalServerError)]
    [HttpPatch("/paymentRequests/{id}/transactions/{transactionId}/retry")]
    public async Task<PaymentTransactionViewModel?> RetryFailedTransaction(
        [FromRoute] Guid id,
        [FromRoute] Guid transactionId,
        [FromHeader] string userId,
        CancellationToken cancellationToken)
    {
        var result = await paymentTransactionService.RetryFailedTransaction(id, transactionId, userId, cancellationToken);

        return mapper.Map<PaymentTransactionViewModel>(result);
    }

    /// <summary>
    /// Retry transaction by payment request id
    /// </summary>
    /// <returns></returns>
    [HttpPatch("{id}/retry")]
    public async Task RetryTransactionByPaymentRequestId([FromRoute] Guid id, [FromHeader] string userId, CancellationToken cancellationToken)
    {
        await paymentTransactionService.RetryFailedTransaction(id, userId, cancellationToken);
    }

    /// <summary>
    /// Manually retry a failed pull from customer transaction
    /// </summary>
    /// <returns></returns>
    [ProducesResponseType(typeof(PaymentTransactionViewModel), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorModel), StatusCodes.Status500InternalServerError)]
    [HttpPatch("/paymentRequests/{id}/transactions/{transactionId}/retry/pull-from-customer")]
    public async Task<PaymentTransactionViewModel> RetryFailedTransaction(
        [FromRoute] Guid id,
        [FromRoute] Guid transactionId,
        [FromHeader] string userId,
        [FromBody] RetryFailedTransactionRequest request,
        CancellationToken cancellationToken)
    {
        var result = await paymentTransactionService.RetryFailedTransaction(id, transactionId, userId, cancellationToken, request.CustomerBankAccountId);
        return mapper.Map<PaymentTransactionViewModel>(result);
    }

    [ProducesResponseType(typeof(PaymentRequestViewModel), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorModel), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorModel), StatusCodes.Status404NotFound)]
    [HttpPut("/paymentRequests/{id}/pause")]
    public async Task<PaymentRequestViewModel> PausePaymentRequest(PausePaymentRequestViewModel request, [FromRoute] Guid id, [FromHeader] string userId, CancellationToken cancellationToken)
    {
        var mappedRequest = mapper.Map<PausePaymentRequestModel>(request);

        var result = await paymentRequestService.UpdatePauseStatus(id, mappedRequest, userId, cancellationToken);
        return mapper.Map<PaymentRequestViewModel>(result);
    }

    [ProducesResponseType(typeof(PaymentRequestViewModel), StatusCodes.Status200OK)]
    [HttpGet("/paymentRequests/total-amount")]
    public Task<decimal> GetTotalAmount(bool includeProcessingStatus, CancellationToken cancellationToken)
    {
        return paymentRequestService.GetTotalAmount(includeProcessingStatus, cancellationToken);
    }

    /// <summary>
    /// Approve manual payment approvalRequest
    /// </summary>
    /// <returns></returns>
    [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorModel), StatusCodes.Status500InternalServerError)]
    [HttpPatch("/paymentRequests/{id}/approve")]
    public async Task ApprovePaymentRequest(
        [FromRoute] Guid id,
        [FromHeader] string createdBy,
        [FromBody] PaymentApprovalRequestViewModel requestViewModel,
        CancellationToken cancellationToken)
    {
        var mappedRequest = mapper.Map<PaymentApprovalRequest>(requestViewModel);
        await paymentRequestService.ApprovePaymentRequest(id, createdBy, mappedRequest, cancellationToken);
    }

    /// <summary>
    /// Mark payment request as succeeded
    /// </summary>
    /// <returns></returns>
    [ProducesResponseType(typeof(PaymentRequestViewModel), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorModel), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorModel), StatusCodes.Status404NotFound)]
    [HttpPatch("{id}/mark-payment-request-succeeded")]
    public async Task<PaymentRequestViewModel> MarkPaymentRequestSucceeded(
        [FromRoute] Guid id,
        [FromBody] MarkPaymentRequestSucceededViewModel request,
        [FromHeader] string updatedBy,
        CancellationToken cancellationToken)
    {
        var mappedRequest = mapper.Map<MarkPaymentRequestSucceededModel>(request);
        var result = await paymentRequestService.MarkPaymentRequestAsSucceeded(id, mappedRequest, updatedBy, cancellationToken);
        return mapper.Map<PaymentRequestViewModel>(result);
    }

    /// <summary>
    /// Get accounts
    /// </summary>
    /// <returns></returns>
    [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorModel), StatusCodes.Status500InternalServerError)]
    [HttpGet("accounts")]
    public async Task<List<AccountResponseObj?>?> GetAccount([FromQuery] string? paymentProvider, CancellationToken cancellationToken)
    {
        if (paymentProvider != "aion")
        {
            return null;
        }

        return await aionServiceV2.GetAccounts(cancellationToken);
    }
}