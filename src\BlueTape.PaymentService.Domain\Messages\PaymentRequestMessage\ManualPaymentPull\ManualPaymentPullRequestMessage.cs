﻿using System.Text.Json.Serialization;

namespace BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.ManualPaymentPull;

public class ManualPaymentPullRequestMessage : BasePaymentRequestMessage
{
    [JsonPropertyName("details")]
    public ManualPaymentPullDetails PaymentRequestDetails { get; set; } = new ManualPaymentPullDetails();
}

public class ManualPaymentPullDetails
{
    [JsonPropertyName("date")]
    public DateTime Date { get; set; }

    [JsonPropertyName("currency")]
    public string Currency { get; set; } = string.Empty;

    [JsonPropertyName("requestedAmount")]
    public decimal RequestedAmount { get; set; }

    [JsonPropertyName("paymentMethod")]
    public string PaymentMethod { get; set; } = "ach";

    [JsonPropertyName("customerDetails")]
    public CustomerDetails CustomerDetails { get; set; } = new CustomerDetails();

    [JsonPropertyName("manualAccountCode")]
    public string? ManualAccountCode { get; set; }

    [JsonPropertyName("manualPaymentDetails")]
    public ManualPaymentDetails ManualPaymentDetails { get; set; } = new ManualPaymentDetails();
}
