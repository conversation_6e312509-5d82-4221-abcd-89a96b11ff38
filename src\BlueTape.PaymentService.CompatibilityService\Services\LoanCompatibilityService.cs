﻿using BlueTape.LS.DTOs.Loan;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Service;
using BlueTape.PaymentService.DataAccess.External.Abstractions.Services;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.LoanApplication;
using Microsoft.Extensions.Logging;
using Polly;
using Polly.Retry;

namespace BlueTape.PaymentService.CompatibilityService.Services;
public class LoanCompatibilityService(
    ILogger<LoanCompatibilityService> logger,
    ILoanApplicationRepository loanApplicationRepository,
    ILmsExternalService lmsService
    ) : ILoanCompatibilityService
{
    public async Task SyncLoanApplicationFields(Guid? lmsId, CancellationToken cancellationToken)
    {
        if (!lmsId.HasValue)
        {
            logger.LogWarning("Unable to sync loan application fields: DrawId is null");
            return;
        }

        var loanApplication = await loanApplicationRepository.GetByLmsId(lmsId.ToString(), cancellationToken);
        if (loanApplication == null)
        {
            logger.LogWarning("Unable to sync loan application fields: Loan application not found for LmsId {LmsId}", lmsId);
            return;
        }

        AsyncRetryPolicy<LoanDto?> retryPolicy = Policy
            .Handle<Exception>()
            .OrResult<LoanDto?>(loan => loan == null)
            .WaitAndRetryAsync(3, retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
                (exception, timeSpan, retryCount, context) =>
                {
                    logger.LogWarning("Retry {RetryCount} for LmsId {LmsId} due to: {ExceptionMessage}. Waited {TimeSpan}s",
                        retryCount, lmsId, exception.Exception?.Message ?? "the loan is empty", timeSpan.TotalSeconds);
                });

        var loan = await retryPolicy.ExecuteAsync(() => lmsService.GetLoanDetailedById(lmsId, cancellationToken));

        if (loan == null)
        {
            logger.LogWarning("Unable to get loan details for LmsId {LmsId} after multiple retries.", lmsId);
            return;
        }
        
        var nextPaymentDate = loan.LoanDetails?.NextPaymentDate;
        var nextPaymentAmount = loan.LoanDetails?.NextPaymentAmount;
        var remainingAmount = loan.LoanDetails?.LoanOutstandingAmount;

        var updateDocument = new UpdateLoanApplicationDocument()
        {
            LastPaymentDate = loan.LastPaymentDate?.ToString("yyyy-MM-dd"),
            NextPaymentDate = nextPaymentDate?.ToString("yyyy-MM-dd"),
            NextPaymentAmount = nextPaymentAmount,
            RemainingAmount = remainingAmount ?? 0,
            ProcessingAmount = loan.LoanDetails?.TotalProcessingPaymentsAmount ?? 0,
            PastDueAmount = loan.LoanDetails?.LateAmount ?? 0,
        };

        await loanApplicationRepository.Update(loanApplication.BlueTapeId, updateDocument, cancellationToken);

        logger.LogInformation("Successfully synced loan application fields for LmsId {LmsId}", lmsId);
    }
}
