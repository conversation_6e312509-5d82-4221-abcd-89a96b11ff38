﻿using BlueTape.PaymentService.DataAccess.Mongo.Entities.Invoice;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.BaseDisbursement;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepaymentCard;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepaymentManual;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.SubscriptionFeePayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.InvoicePayment;
using BlueTape.PaymentService.TestUtilities.Models;
using BlueTape.Integrations.Aion.Infrastructure.Enums;

namespace BlueTape.PaymentService.TestUtilities.Abstractions.Services;

public interface ITestingService
{
    Task<InvoiceEntity> CreateInvoice(string? supplierEmail, string? customerEmail, double? amount, CancellationToken ct);
    Task CreateBunchOfPaymentRequests(string? supplierEmail, string? customerEmail, double? amount, CancellationToken ct);
    Task<string> CreatePaymentRequest(string? supplierEmail, string? customerEmail, double? amount, string? invoiceId, CancellationToken ct, bool onlyMessage = false);
    Task<string> CreatePaymentRequestV2Flow(string? supplierEmail, string? customerEmail, double? amount, string? invoiceId, CancellationToken ct, bool onlyMessage = false);
    Task<BaseDisbursementRequestMessage> CreateDisbursementPayment(string? supplierEmail, string? customerEmail, double? amount, PaymentRequestType type, string? invoiceId, CancellationToken ct, bool onlyMessage = false);
    Task<DrawRepaymentRequestMessage> CreateDrawRepayment(string? supplierEmail, string? customerEmail, double? amount, string? invoiceId, CancellationToken ct, bool onlyMessage = false);

    Task<DrawRepaymentCardRequestMessage> CreateDrawRepaymentCard(string supplierEmail,
        string customerEmail, double? amount, string? invoiceId, CancellationToken ct, bool onlyMessage = false);

    Task<DrawRepaymentManualRequestMessage> CreateDrawRepaymentManual(string supplierEmail,
        string customerEmail, double? amount, string? invoiceId, CancellationToken ct, bool onlyMessage = false);


    Task<string> CreateFactoringFlow(string? supplierEmail, string? customerEmail, double? amount, CancellationToken ct);

    Task<string> CreateTradeCreditFlow(string? supplierEmail, string? customerEmail, double? amount,
        CancellationToken ct);

    Task<string> InitializeAionTransactionsStatusMovement(Guid? paymentRequestId, TransactionStatus status, CancellationToken ct);

    Task<string> InitializeStatusMovementForBanchOfPayments(CancellationToken ct);
    Task<SubscriptionFeePaymentRequestMessage> CreateSubscriptionFeePayment(string? supplierEmail, string? customerEmail,
        double? amount, string? invoiceId, CancellationToken ct, bool onlyMessage = false);

    Task<InvoicePaymentRequestMessage?> SendInvoicePaymentV2RequestMessage(
        CreateInvoicePaymentV2Request request, string userId, PaymentRequestType requestType, CancellationToken ct);

    Task<string> ChangePaymentSubscription(Guid paymentRequestId, PaymentSubscriptionType newSubscriptionType, CancellationToken ct);
}
