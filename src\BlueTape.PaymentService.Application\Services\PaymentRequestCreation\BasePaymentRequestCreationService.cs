﻿using AutoMapper;
using BlueTape.InvoiceClient.Abstractions;
using BlueTape.InvoiceService.Models.Invoice;
using BlueTape.LS.DTOs.Loan;
using BlueTape.PaymentService.Application.Abstractions.Senders;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation.Base;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.DataAccess.External.Models;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Enums.Legacy;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Abstractions;
using BlueTape.PaymentService.UnitOfWork.Abstractions;
using BlueTape.ServiceBusMessaging.Attributes;
using Microsoft.Extensions.Logging;
using TinyHelpers.Extensions;

namespace BlueTape.PaymentService.Application.Services.PaymentRequestCreation;

public abstract class BasePaymentRequestCreationService(
        IMapper mapper,
        IUnitOfWork unitOfWork,
        IPaymentFlowTemplatesEngine templatesEngine,
        IInvoiceHttpClient invoiceHttpClient,
        ILogger<BasePaymentRequestCreationService> logger,
        IOperationSyncMessageSender operationSyncMessageSender,
        IPaymentRequestPayableService paymentRequestPayableService,
        ILoanManagementService loanManagementService) : IBasePaymentRequestCreationService
{
    private static readonly PaymentRequestType[] DisbursementRequestTypes =
    [
        PaymentRequestType.DrawDisbursement,
        PaymentRequestType.FactoringDisbursement,
        PaymentRequestType.FinalPayment,
        PaymentRequestType.FinalPaymentV2,
        PaymentRequestType.FactoringFinalPayment
    ];

    public async Task<PaymentRequestModel> Add(CreatePaymentRequestModel createPaymentRequest, string createdBy, CancellationToken ct)
    {
        var invoiceIds = createPaymentRequest.PaymentRequestPayables.Select(x => x.Id).ToArray();
        var existingInvoicesTask = invoiceHttpClient.GetInvoicesByIdsAsync(invoiceIds, ct);
        var processedPayablesTask = paymentRequestPayableService.GetByPayableIds(invoiceIds.ToList(), ct);

        // Determine which loan to fetch based on available information
        Task<LoanDto?> loansTask;
        if (createPaymentRequest.DrawId is not null)
        {
            // If we have a DrawId, fetch the loan directly by ID
            loansTask = loanManagementService.GetLoanById(createPaymentRequest.DrawId, ct);
        }
        else if (invoiceIds.Length > 0)
        {
            // If we have invoice IDs but no DrawId, try to find a loan by the first invoice ID
            loansTask = loanManagementService.FindLoan(
                new LoanQuery { PayableId = invoiceIds[0], Detailed = true }, ct);
        }
        else
        {
            // If we have neither DrawId nor invoice IDs, return null
            loansTask = Task.FromResult<LoanDto?>(null);
        }

        await Task.WhenAll(existingInvoicesTask, processedPayablesTask, loansTask);

        var existingInvoices = await existingInvoicesTask ?? new List<InvoiceModel>();
        var processedPayables = await processedPayablesTask ?? new List<PaymentRequestPayableModel>();
        var loan = await loansTask;

        createPaymentRequest.FundingSource = loan?.FundingSource;

        await ValidatePaymentRequest(createPaymentRequest, existingInvoices, processedPayables, loan, ct);

        var paymentRequestEntity = mapper.Map<PaymentRequestEntity>(createPaymentRequest);
        var templateDetails = templatesEngine.GetPaymentRequestTemplateDetails(createPaymentRequest.FundingSource, createPaymentRequest.FlowTemplateCode);
        paymentRequestEntity.Status = PaymentRequestStatus.Requested;
        paymentRequestEntity.CreatedBy = createdBy;
        paymentRequestEntity.PaymentSubscription = templateDetails.PaymentSubscription;

        if (paymentRequestEntity.Date == DateOnly.MinValue)
            paymentRequestEntity.Date = DateOnly.FromDateTime(DateTime.Now);

        BuildPaymentRequestDetails(createPaymentRequest, paymentRequestEntity, ct);

        // Temporary set required confirmation for all disbursement types
        if (DisbursementRequestTypes.Contains(paymentRequestEntity.RequestType))
            paymentRequestEntity.ConfirmationType = ConfirmationType.Manual;

        var isCompanyPaymentsForbidden = await CheckCompaniesPermission([createPaymentRequest.PayeeId!, createPaymentRequest.PayerId!], paymentRequestEntity, ct);

        await unitOfWork.PaymentRequestRepository.Insert(paymentRequestEntity, ct);

        var transactions = new List<PaymentTransactionEntity>();
        var commands = new List<PaymentRequestCommandEntity>();
        var preGeneratedTransactions = await templatesEngine.PreGenerateTransactionsBasedOnTemplate(createPaymentRequest);
        foreach (var preGeneratedTransaction in preGeneratedTransactions)
        {
            var transaction = mapper.Map<PaymentTransactionEntity>(preGeneratedTransaction);
            transaction.CreatedBy = createdBy;
            transaction.PaymentRequestId = paymentRequestEntity.Id;
            transaction.PaymentMethod = paymentRequestEntity.PaymentMethod;
            transaction.Provider = PaymentProvider.Aion;

            var command = new PaymentRequestCommandEntity
            {
                PaymentRequestId = paymentRequestEntity.Id,
                Status = CommandStatus.Placed,
                CreatedBy = createdBy,
                StepName = preGeneratedTransaction.StepName,
                Transaction = transaction,
            };
            transactions.Add(transaction);
            commands.Add(command);
        }

        var firstCommand = commands.MinBy(x => x.Transaction?.SequenceNumber);

        if (!isCompanyPaymentsForbidden)
            firstCommand!.Status = CommandStatus.Pending;

        await unitOfWork.PaymentRequestCommandRepository.InsertRange(commands, ct);
        await unitOfWork.PaymentTransactionRepository.InsertRange(transactions, ct);

        await unitOfWork.SaveAsync(ct);

        logger.LogInformation("Payment request has been created successfully, Id: {id}", paymentRequestEntity.Id);

        await SendNotification(createPaymentRequest, paymentRequestEntity, existingInvoices, ct);

        await AbortFailedRequestsForInvoices(processedPayables, paymentRequestEntity.RequestType, ct);

        await CreateLmsPayment(paymentRequestEntity, ct);

        await operationSyncMessageSender.SendMessage(new ServiceBusMessageBt<SyncOperationMessagePayload>(
            new SyncOperationMessagePayload
            {
                PaymentRequestId = paymentRequestEntity.Id,
                SyncType = SyncType.PerformOperation
            }), ct);

        return mapper.Map<PaymentRequestModel>(paymentRequestEntity);
    }

    protected abstract Task SendNotification(CreatePaymentRequestModel createPaymentRequest, PaymentRequestEntity paymentRequestEntity, IEnumerable<InvoiceModel> existingInvoices, CancellationToken ct);
    protected abstract Task ValidatePaymentRequest(CreatePaymentRequestModel createPaymentRequest,
        List<InvoiceModel> existingInvoices, List<PaymentRequestPayableModel> processedPayables, LoanDto? loan,
        CancellationToken ct);

    /// <remarks>
    /// This method handles two main scenarios:
    /// 1. Line of Credit (LOC) and ARA (IHC) cases - populates draw/payment reference details
    /// 2. Manual payment cases - populates external reference and metadata
    /// </remarks>
    protected void BuildPaymentRequestDetails(CreatePaymentRequestModel request, PaymentRequestEntity paymentRequestEntity, CancellationToken ct)
    {
        var hasLoanDetails = request.DrawId.HasValue ||
                             request.LmsPaymentId.HasValue ||
                             request.FundingSource.HasValue ||
                             !string.IsNullOrEmpty(request.ParentReferenceNumber);

        if (hasLoanDetails)
        {
            paymentRequestEntity.PaymentRequestDetails ??= new PaymentRequestDetailsEntity();
            paymentRequestEntity.PaymentRequestDetails.DrawId = request.DrawId;
            paymentRequestEntity.PaymentRequestDetails.LMSPaymentId = request.LmsPaymentId;
            paymentRequestEntity.PaymentRequestDetails.ParentReferenceNumber = request.ParentReferenceNumber;
            paymentRequestEntity.PaymentRequestDetails.FundingSource = request.FundingSource;
        }

        if (request.ManualPaymentDetails?.UserId != null)
        {
            paymentRequestEntity.PaymentRequestDetails ??= new PaymentRequestDetailsEntity();
            paymentRequestEntity.PaymentRequestDetails.ExternalReferenceNumber = request.ManualPaymentDetails?.ExternalReferenceNumber; //Aion transaction id in that case
            paymentRequestEntity.PaymentRequestDetails.SetMetadata(MetadataConstants.ManualPaymentDetails, request.ManualPaymentDetails);
        }
        if (request.AdditionalDetails is not null)
        {
            paymentRequestEntity.PaymentRequestDetails ??= new PaymentRequestDetailsEntity();
            paymentRequestEntity.PaymentRequestDetails.InvoiceNumber = request.AdditionalDetails.InvoiceNumber;
            paymentRequestEntity.PaymentRequestDetails.Reason = request.AdditionalDetails.Reason;
        }
    }

    protected async Task<bool> CheckCompaniesPermission(IEnumerable<string> companyIds, PaymentRequestEntity paymentRequestEntity, CancellationToken ct)
    {
        // Do not put it to Task.WhenAll, otherwise DbContext fail with concurrency exception
        var isCompanyPaymentsForbidden = await unitOfWork.AreCompaniesPaymentsForbidden(companyIds, ct);

        if (!isCompanyPaymentsForbidden)
            return false;

        paymentRequestEntity.PaymentRequestDetails ??= new PaymentRequestDetailsEntity();

        paymentRequestEntity.PaymentRequestDetails.IsPaused = true;
        paymentRequestEntity.PaymentRequestDetails.PauseReason = PaymentPauseReason.ForbiddenCompany;
        paymentRequestEntity.PaymentRequestDetails.PauseComments = "Paused during the payment request creation";

        return true;
    }

    protected async Task CreateLmsPayment(PaymentRequestEntity paymentRequest, CancellationToken ct)
    {
        // DrawRepayment LMS payment creates on NJS side
        var shouldPaymentBeCreatedInLms = ShouldPaymentBeCreatedInLms(paymentRequest);
        if (!shouldPaymentBeCreatedInLms) return;

        var payableIds = paymentRequest.PaymentRequestPayables.Select(x => x.PayableId).ToArray();
        var lmsPayment = await loanManagementService.CreateLmsPayment(paymentRequest.Id, payableIds, paymentRequest?.PaymentRequestDetails?.DrawId, paymentRequest.Amount, ct);
        var paymentRequestDetails = paymentRequest.PaymentRequestDetails ??= new PaymentRequestDetailsEntity();
        paymentRequestDetails.LMSPaymentId = lmsPayment?.Id;
        paymentRequestDetails.DrawId = lmsPayment?.LoanId;

        await unitOfWork.PaymentRequestRepository.Update(paymentRequest, ct);
        await unitOfWork.SaveAsync(ct);
    }

    protected async Task AbortFailedRequestsForInvoices(IEnumerable<PaymentRequestPayableModel>? processedPayables, PaymentRequestType type, CancellationToken ct)
    {
        var paymentRequestPayableModels = processedPayables?.ToList();
        if (paymentRequestPayableModels is null || !paymentRequestPayableModels.Any())
        {
            logger.LogInformation("Create payment request: No payment request to abort.");
            return;
        }

        var paymentRequestIds = paymentRequestPayableModels
            .Where(x => x.PaymentRequest!.Status == PaymentRequestStatus.Failed && x.PaymentRequest.RequestType == type)
            .Select(x => x.PaymentRequestId)
            .ToList();

        var paymentRequests = (await unitOfWork.Get<PaymentRequestEntity>(ct, x => paymentRequestIds.Contains(x.Id))).ToList();

        foreach (var request in paymentRequests)
        {
            request.Status = PaymentRequestStatus.Aborted;

            if (request.RequestType == PaymentRequestType.InvoicePaymentV2)
                await loanManagementService.RejectLmsPayment(request.Id, request.PaymentRequestDetails!.LMSPaymentId, ct);
        }

        await unitOfWork.PaymentRequestRepository.UpdateRange(paymentRequests, ct);
        await unitOfWork.SaveAsync(ct);

        logger.LogInformation($"Create payment request: Some payment requests marked as aborted, paymentRequestIds: {string.Join(", ", paymentRequestIds)}.");
    }

    private static bool ShouldPaymentBeCreatedInLms(PaymentRequestEntity paymentRequest)
    {
        var requestType = paymentRequest.RequestType;

        if (requestType is PaymentRequestType.DrawRepayment && paymentRequest.PaymentRequestDetails?.LMSPaymentId != null)
        {
            return false;
        }

        if (requestType
         is not PaymentRequestType.InvoicePaymentV2
         and not PaymentRequestType.DrawRepaymentManual
         and not PaymentRequestType.DrawRepayment
         and not PaymentRequestType.DrawRepaymentCard)
            return false;

        return true;
    }

    protected static List<CreatePaymentRequestFeeModel>? GetMerchantFeesFromPayablesDiscount(IEnumerable<CreatePaymentRequestPayableModel> payables, string companyId, FeeType feeType)
    {
        if (payables.IsEmpty())
            return null;

        var createDiscountDetails = payables
            .Where(x => x.Discount != 0)
            .Select(x => new CreatePaymentRequestFeeModel()
            {
                Amount = x.Discount,
                CompanyId = companyId,
                Type = feeType
            })
            .ToList();

        return createDiscountDetails;
    }
}
