﻿using AutoFixture;
using BlueTape.Common.ExceptionHandling.Exceptions;
using BlueTape.CompanyClient.Abstractions;
using BlueTape.CompanyService.Companies;
using BlueTape.PaymentService.Application.Abstractions.Senders;
using BlueTape.PaymentService.Application.Abstractions.Senders.Payments;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Abstractions.Services.Notification;
using BlueTape.PaymentService.Application.Mappers;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Application.Models.Base;
using BlueTape.PaymentService.Application.Services;
using BlueTape.PaymentService.Application.Tests.Constants;
using BlueTape.PaymentService.Application.Tests.Models;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Entities.Base;
using BlueTape.PaymentService.Domain.Entities.Filters;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Exceptions;
using BlueTape.PaymentService.Domain.Extensions;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Abstractions;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Models;
using BlueTape.PaymentService.UnitOfWork.Abstractions;
using BlueTape.PaymentService.UnitOfWork.Records;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Linq.Expressions;
using PaymentRequestEntity = BlueTape.PaymentService.Domain.Entities.PaymentRequestEntity;
using PaymentTransactionEntity = BlueTape.PaymentService.Domain.Entities.PaymentTransactionEntity;

namespace BlueTape.PaymentService.Application.Tests.Services;

public class PaymentRequestServiceTests
{
    private readonly IPaymentRequestRepository _paymentRequestRepository = Substitute.For<IPaymentRequestRepository>();
    private readonly IPaymentFlowTemplatesEngine _templatesEngine = Substitute.For<IPaymentFlowTemplatesEngine>();
    private readonly IPaymentTransactionHistoryService _paymentTransactionHistoryService = Substitute.For<IPaymentTransactionHistoryService>();
    private readonly IUnitOfWork _unitOfWork = Substitute.For<IUnitOfWork>();
    private IMapper _mapper = Substitute.For<IMapper>();
    private readonly IPaymentRequestCommandService _paymentRequestCommandService = Substitute.For<IPaymentRequestCommandService>();
    private readonly ILogger<PaymentRequestService> _logger = Substitute.For<ILogger<PaymentRequestService>>();
    private readonly IOptions<PaymentService.PaymentFlowTemplatesEngine.Models.Raw.RawFlowTemplateOptions> _templateOptionsMock = Substitute.For<IOptions<PaymentService.PaymentFlowTemplatesEngine.Models.Raw.RawFlowTemplateOptions>>();
    private readonly ISlackNotificationService _notificationService = Substitute.For<ISlackNotificationService>();
    private readonly IOperationSyncMessageSender _operationSyncMessageSender = Substitute.For<IOperationSyncMessageSender>();
    private readonly IDateProvider _dateProvider = Substitute.For<IDateProvider>();
    private readonly ICompanyHttpClient _companyHttpClient = Substitute.For<ICompanyHttpClient>();
    private readonly IPaymentRequestService _paymentRequestService = Substitute.For<IPaymentRequestService>();
    private readonly IDrawRepaymentManualMessageSender _messageSender = Substitute.For<IDrawRepaymentManualMessageSender>();
    private readonly IManualPaymentPullMessageSender _manualPaymentPullMessageSender = Substitute.For<IManualPaymentPullMessageSender>();
    private readonly ILoanManagementService _loanManagementService = Substitute.For<ILoanManagementService>();

    private Fixture _fixture = AutoFixtureCustomAttribute.GetFixture();

    public PaymentRequestServiceTests()
    {
        _templateOptionsMock.Value.Returns(TemplateOptions.RawTemplateOptions);

        _paymentRequestService = new PaymentRequestService(_paymentRequestRepository,
            _mapper,
            _unitOfWork,
            _templatesEngine,
            _companyHttpClient,
            _logger,
            _templateOptionsMock,
            _notificationService,
            _operationSyncMessageSender,
            _loanManagementService,
            _dateProvider,
            _messageSender,
            _manualPaymentPullMessageSender);
    }

    private PaymentRequestService GetServiceWithRealMapper()
    {
        var config = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<MessageProfile>();
            cfg.AddProfile<ModelsProfile>();
        });

        var mapper = config.CreateMapper();

        _templateOptionsMock.Value.Returns(TemplateOptions.RawTemplateOptions);

        return new PaymentRequestService(_paymentRequestRepository,
            mapper,
            _unitOfWork,
            _templatesEngine,
            _companyHttpClient,
            _logger,
            _templateOptionsMock,
            _notificationService,
            _operationSyncMessageSender,
            _loanManagementService,
            _dateProvider,
            _messageSender,
            _manualPaymentPullMessageSender);
    }

    [Theory, AutoFixtureCustom]
    public async Task GetPaymentRequests_HasData_ReturnsPaymentRequestList(IEnumerable<PaymentRequestEntity> entities,
        IEnumerable<PaymentRequestModel> models)
    {
        _mapper.Map<IEnumerable<PaymentRequestModel>>(entities).Returns(models);
        _paymentRequestRepository.Get(default).Returns(entities);
        var result = await _paymentRequestService.Get(default);

        result.ShouldBeEquivalentTo(models);
    }

    [Fact]
    public async Task GetPaymentRequest_HasNotData_ReturnsEmptyPaymentRequestList()
    {
        var models = new List<PaymentRequestModel>();
        var entities = new List<PaymentRequestEntity>();

        _mapper.Map<IEnumerable<PaymentRequestModel>>(entities).Returns(models);
        _paymentRequestRepository.Get(default).Returns(entities);

        var result = await _paymentRequestService.Get(default);

        result.ShouldBeEquivalentTo(models);
    }

    [Theory, AutoFixtureCustom]
    public async Task GetByFilter_ValidFilter_ReturnsPaginatedResult(PaginatedResult<PaymentRequestModel> paginatedModel, PaginatedEntityResult<PaymentRequestEntity> paginatedEntity, PaymentRequestFilter filter)
    {
        _paymentRequestRepository.GetByFilter(filter, default).Returns(paginatedEntity);
        _mapper.Map<PaginatedResult<PaymentRequestModel>>(paginatedEntity).Returns(paginatedModel);

        var result = await _paymentRequestService.GetByFilter(filter, default);

        result.ShouldBeEquivalentTo(paginatedModel);
    }

    [Theory, AutoFixtureCustom]
    public async Task GetByPayableId_ValidId_ReturnsPaymentRequests(IEnumerable<PaymentRequestEntity> entities, IEnumerable<PaymentRequestModel> models)
    {
        var id = Guid.NewGuid().ToString();
        _paymentRequestRepository.GetByPayableId(id, default).Returns(entities);
        _mapper.Map<IEnumerable<PaymentRequestModel>>(entities).Returns(models);

        var result = await _paymentRequestService.GetByPayableId(id, default);

        result.ShouldBeEquivalentTo(models);
    }

    [Fact]
    public Task GetByPayableId_InvalidPayableId_ReturnsPaymentRequests()
    {
        var id = Guid.NewGuid().ToString();
        _paymentRequestRepository.GetByPayableId(id, default).Returns(Enumerable.Empty<PaymentRequestEntity>());

        var act = async () => await _paymentRequestService.GetByPayableId(id, default);

        return act.ShouldThrowAsync<PaymentRequestDoesNotExistException>();
    }

    [Theory, AutoFixtureCustom]
    public async Task GetPaymentRequestById_ValidId_ReturnsPaymentRequestById(PaymentRequestEntity entity, PaymentRequestModel model)
    {
        _mapper.Map<PaymentRequestModel>(entity).Returns(model);
        _paymentRequestRepository.GetById(model.Id, default).Returns(entity);

        var result = await _paymentRequestService.GetById(model.Id, default);

        result.ShouldBeEquivalentTo(model);
    }

    [Fact]
    public async Task GetPaymentRequestById_InvalidId_ReturnsNull()
    {
        var id = Guid.NewGuid();

        _mapper.Map<PaymentRequestModel>(Arg.Any<PaymentRequestEntity>()).Returns((PaymentRequestModel?)null);
        _paymentRequestRepository.GetById(id, default).Returns((PaymentRequestEntity?)null);

        var result = await _paymentRequestService.GetById(id, default);

        result.ShouldBeNull();
    }

    [Theory, AutoFixtureCustom]
    public Task DeletePaymentRequest_ValidId_ReturnsNull(Guid id)
    {
        return _paymentRequestService.Delete(id, default).ShouldNotThrowAsync();
    }

    [Theory, AutoFixtureCustom]
    public async Task UpdatePaymentRequest_ValidPaymentRequest_ReturnsPaymentRequest(PaymentRequestEntity entity, PaymentRequestModel model)
    {
        _mapper.Map<PaymentRequestModel>(entity).Returns(model);
        _mapper.Map<PaymentRequestEntity>(model).Returns(entity);
        _paymentRequestRepository.Update(entity, default).Returns(entity);

        var result = await _paymentRequestService.Update(model, default);

        result.ShouldBeEquivalentTo(model);
    }

    [Theory, AutoFixtureCustom]
    public async Task UpdatePaymentRequest_InvalidPaymentRequest_ReturnsNull(PaymentRequestEntity entity, PaymentRequestModel model)
    {
        _mapper.Map<PaymentRequestEntity>(model).Returns(entity);
        _mapper.Map<PaymentRequestModel>(Arg.Any<PaymentRequestEntity>()).Returns((PaymentRequestModel?)null);

        var result = await _paymentRequestService.Update(model, default);

        result.ShouldBeNull();
    }

    [Theory, AutoFixtureCustom]
    public async Task AddPaymentRequest_ValidPaymentRequest_ReturnsPaymentRequest(PaymentRequestEntity entity, PaymentRequestModel model)
    {
        _mapper.Map<PaymentRequestModel>(entity).Returns(model);
        _mapper.Map<PaymentRequestEntity>(model).Returns(entity);
        _paymentRequestRepository.Add(entity, default).Returns(entity);

        var result = await _paymentRequestService.Add(model, default);

        result.ShouldBeEquivalentTo(model);
    }

    [Theory, AutoFixtureCustom]
    public void GetPaymentRequestsByPredicate_ValidPredicate_ReturnsPaymentRequestList(IEnumerable<PaymentRequestModel> models,
        Expression<Func<PaymentRequestModel, bool>> predicateModel, Expression<Func<PaymentRequestEntity, bool>> predicateEntities)
    {
        _mapper.Map<IEnumerable<PaymentRequestModel>>(Arg.Any<IEnumerable<PaymentRequestEntity>>()).Returns(models);
        _mapper.Map<Expression<Func<PaymentRequestEntity, bool>>>(Arg.Any<Expression<Func<PaymentRequestModel, bool>>>()).Returns(predicateEntities);
        var result = _paymentRequestService.Get(predicateModel, default);

        result.ShouldNotBeNull();
    }

    [Fact]
    public Task ChangeStatus_PaymentRequestDoesNotExist_ThrowsVariableNullException()
    {
        var id = Guid.NewGuid();

        var act = async () => await _paymentRequestService.CancelPaymentRequest(id, string.Empty, default);

        return act.ShouldThrowAsync<VariableNullException>();
    }

    [Theory, AutoFixtureCustom]
    public async Task ChangeStatus_PaymentRequestExists_SavesDataInDatabase(PaymentRequestEntity paymentRequest,
        List<PaymentTransactionEntity> transactions, PaymentTransactionPayload historyItem)
    {
        var id = Guid.NewGuid();
        var createdBy = id.ToString();
        paymentRequest.Transactions = transactions;
        transactions.ForEach(x => x.PaymentRequestId = paymentRequest.Id);

        _paymentRequestRepository.GetById(id, default).Returns(paymentRequest);
        _paymentTransactionHistoryService.BuildPaymentTransactionHistoryPayload(
            Arg.Is<PaymentTransactionEntity>(t => t.PaymentRequestId == paymentRequest.Id), TransactionStatus.Canceled,
            createdBy).Returns(historyItem);

        await _paymentRequestService.CancelPaymentRequest(id, createdBy, default);

        _unitOfWork.Received(5);
        await _unitOfWork.Received(1).SaveAsync(default);
    }

    [Fact]
    public async Task CalculatePaymentRequestStatus_Returns_Settled()
    {
        var paymentRequestId = Guid.NewGuid();
        var templateId = _templateOptionsMock.Value.Templates.First().FlowTemplateCode!;

        var commands = new List<PaymentRequestCommandEntity>
        {
            new()
            {
                PaymentRequest = new PaymentRequestEntity() {FlowTemplateCode = templateId},
                StepName = "PullFromCustomer",
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity() {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = "CollectToFunding",
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = "CollectToRevenue",
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = "PushToMerchant",
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
        };

        _unitOfWork.GetCommandsByPaymentRequestId(Arg.Any<Guid>(), default)
            .Returns(commands);

        var result = await GetServiceWithRealMapper().CalculatePaymentRequestStatus(paymentRequestId, default);
        result.ShouldBeEquivalentTo(PaymentRequestStatus.Settled);
    }

    [Fact]
    public async Task CalculatePaymentRequestStatus_Returns_Settled_With_Retried_Transaction()
    {
        var paymentRequestId = Guid.NewGuid();
        var templateId = _templateOptionsMock.Value.Templates.First().FlowTemplateCode!;

        var commands = new List<PaymentRequestCommandEntity>
        {
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = "PullFromCustomer",
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = "CollectToFunding",
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = "CollectToRevenue",
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = "PushToMerchant",
                Status = CommandStatus.Retried,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Error}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity{FlowTemplateCode = templateId},
                StepName = "PushToMerchant",
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
        };

        _unitOfWork.GetCommandsByPaymentRequestId(Arg.Any<Guid>(), default)
            .Returns(commands);

        var result = await GetServiceWithRealMapper().CalculatePaymentRequestStatus(paymentRequestId, default);
        result.ShouldBeEquivalentTo(PaymentRequestStatus.Settled);
    }

    [Theory]
    [InlineData(TransactionStatus.Failed)]
    [InlineData(TransactionStatus.Error)]
    public async Task CalculatePaymentRequestStatus_Returns_Failed_Due_To_Failed_Transaction(TransactionStatus transactionFailedStatus)
    {
        var paymentRequestId = Guid.NewGuid();
        var templateId = _templateOptionsMock.Value.Templates.First().FlowTemplateCode!;

        var commands = new List<PaymentRequestCommandEntity>
        {
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = "PullFromCustomer",
                Status = CommandStatus.Failed,
                Transaction = new PaymentTransactionEntity {Status = transactionFailedStatus}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = "CollectToFunding",
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = "CollectToRevenue",
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = "PushToMerchant",
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
        };

        _unitOfWork.GetCommandsByPaymentRequestId(Arg.Any<Guid>(), default)
            .Returns(commands);

        var result = await GetServiceWithRealMapper().CalculatePaymentRequestStatus(paymentRequestId, default);
        result.ShouldBeEquivalentTo(PaymentRequestStatus.Failed);
    }

    [Theory]
    [InlineData(TransactionStatus.Recalled)]
    public async Task CalculatePaymentRequestStatus_Returns_Aborted_Due_To_RollBack_Finished(TransactionStatus transactionFailedStatus)
    {
        var paymentRequestId = Guid.NewGuid();
        var templateId = _templateOptionsMock.Value.Templates.First().FlowTemplateCode!;

        var commands = new List<PaymentRequestCommandEntity>
        {
            new()
            {
                PaymentRequest = new PaymentRequestEntity() {FlowTemplateCode = templateId},
                StepName = StepName.PullFromCustomer.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity() {Status = transactionFailedStatus}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.CollectToFunding.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.CollectToRevenue.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.PushToMerchant.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },

            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.PullFromMerchant.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.RevenueToCollect.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.FundingToCollect.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.CollectFromMerchant.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
        };

        _unitOfWork.GetCommandsByPaymentRequestId(Arg.Any<Guid>(), default)
            .Returns(commands);

        var result = await GetServiceWithRealMapper().CalculatePaymentRequestStatus(paymentRequestId, default);
        result.ShouldBeEquivalentTo(PaymentRequestStatus.Aborted);
    }

    [Theory]
    [InlineData(TransactionStatus.Recalled)]
    public async Task CalculatePaymentRequestStatus_Returns_Processing_Due_To_RollBack_PulFromMerchantNotFinished(TransactionStatus transactionFailedStatus)
    {
        var paymentRequestId = Guid.NewGuid();
        var templateId = _templateOptionsMock.Value.Templates.First().FlowTemplateCode!;

        var commands = new List<PaymentRequestCommandEntity>
        {
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.PullFromCustomer.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = transactionFailedStatus}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.CollectToFunding.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.CollectToRevenue.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.PushToMerchant.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },

            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.PullFromMerchant.ToString(),
                Status = CommandStatus.Executing,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Processing}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.RevenueToCollect.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.FundingToCollect.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.CollectFromMerchant.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
        };

        _unitOfWork.GetCommandsByPaymentRequestId(Arg.Any<Guid>(), default)
            .Returns(commands);

        var result = await GetServiceWithRealMapper().CalculatePaymentRequestStatus(paymentRequestId, default);
        result.ShouldBeEquivalentTo(PaymentRequestStatus.Processing);
    }

    [Theory]
    [InlineData(TransactionStatus.Recalled)]
    public async Task CalculatePaymentRequestStatus_Returns_Processing_Due_To_RollBack_GeneratedButNotStarted(TransactionStatus transactionFailedStatus)
    {
        var paymentRequestId = Guid.NewGuid();
        var templateId = _templateOptionsMock.Value.Templates.First().FlowTemplateCode!;

        var commands = new List<PaymentRequestCommandEntity>
        {
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.PullFromCustomer.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = transactionFailedStatus}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.CollectToFunding.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.CollectToRevenue.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.PushToMerchant.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },

            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.PullFromMerchant.ToString(),
                Status = CommandStatus.Placed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Placed}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.RevenueToCollect.ToString(),
                Status = CommandStatus.Placed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Placed}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.FundingToCollect.ToString(),
                Status = CommandStatus.Placed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Placed}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.CollectFromMerchant.ToString(),
                Status = CommandStatus.Placed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Placed}
            },
        };

        _unitOfWork.GetCommandsByPaymentRequestId(Arg.Any<Guid>(), default)
            .Returns(commands);

        var result = await GetServiceWithRealMapper().CalculatePaymentRequestStatus(paymentRequestId, default);
        result.ShouldBeEquivalentTo(PaymentRequestStatus.Processing);
    }

    [Fact]
    public async Task CalculatePaymentRequestStatus_Returns_Processing_Due_To_Receive_RollBack_InProgress()
    {
        var paymentRequestId = Guid.NewGuid();
        var templateId = _templateOptionsMock.Value.Templates.First().FlowTemplateCode!;

        var commands = new List<PaymentRequestCommandEntity>
        {
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = "PullFromCustomer",
                Status = CommandStatus.Failed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Recalled}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = "CollectToFunding",
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = "CollectToRevenue",
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = "PushToMerchant",
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
        };

        _unitOfWork.GetCommandsByPaymentRequestId(Arg.Any<Guid>(), default)
            .Returns(commands);

        var result = await GetServiceWithRealMapper().CalculatePaymentRequestStatus(paymentRequestId, default);
        result.ShouldBeEquivalentTo(PaymentRequestStatus.Processing);
    }

    [Theory]
    [InlineData(TransactionStatus.Processing)]
    [InlineData(TransactionStatus.Placed)]
    [InlineData(TransactionStatus.Processed)]
    public async Task CalculatePaymentRequestStatus_Returns_Processing_Due_To_RollBack_In_Progress__With_Retried_Transaction(TransactionStatus transactionFailedStatus)
    {
        var paymentRequestId = Guid.NewGuid();
        var templateId = _templateOptionsMock.Value.Templates.First().FlowTemplateCode!;

        var commands = new List<PaymentRequestCommandEntity>
        {
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.PullFromCustomer.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Recalled}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.CollectToFunding.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.CollectToRevenue.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.PushToMerchant.ToString(),
                Status = CommandStatus.Retried,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Error}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.PushToMerchant.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },

            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.PullFromMerchant.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = transactionFailedStatus}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.RevenueToCollect.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Placed}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.CollectToFunding.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Placed}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.CollectFromMerchant.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Placed}
            },
        };

        _unitOfWork.GetCommandsByPaymentRequestId(Arg.Any<Guid>(), default)
            .Returns(commands);

        var result = await GetServiceWithRealMapper().CalculatePaymentRequestStatus(paymentRequestId, default);
        result.ShouldBeEquivalentTo(PaymentRequestStatus.Processing);
    }

    [Fact]
    public async Task CalculatePaymentRequestStatus_Returns_Requested()
    {
        var paymentRequestId = Guid.NewGuid();
        var templateId = _templateOptionsMock.Value.Templates.First().FlowTemplateCode!;

        var commands = new List<PaymentRequestCommandEntity>
        {
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.PullFromCustomer.ToString(),
                Status = CommandStatus.Placed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Placed}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.CollectToFunding.ToString(),
                Status = CommandStatus.Placed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Placed}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.CollectToRevenue.ToString(),
                Status = CommandStatus.Placed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Placed}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.PushToMerchant.ToString(),
                Status = CommandStatus.Placed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Placed}
            }
        };

        _unitOfWork.GetCommandsByPaymentRequestId(Arg.Any<Guid>(), default)
            .Returns(commands);

        var result = await GetServiceWithRealMapper().CalculatePaymentRequestStatus(paymentRequestId, default);
        result.ShouldBeEquivalentTo(PaymentRequestStatus.Requested);
    }

    [Fact]
    public async Task CalculatePaymentRequestStatus_Returns_Cancelled()
    {
        var paymentRequestId = Guid.NewGuid();
        var templateId = _templateOptionsMock.Value.Templates.First().FlowTemplateCode!;

        var commands = new List<PaymentRequestCommandEntity>
        {
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId, Status = PaymentRequestStatus.Cancelled},
                StepName = StepName.PullFromCustomer.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId, Status = PaymentRequestStatus.Cancelled},
                StepName = StepName.CollectToFunding.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId, Status = PaymentRequestStatus.Cancelled},
                StepName = StepName.CollectToRevenue.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId, Status = PaymentRequestStatus.Cancelled},
                StepName = StepName.PushToMerchant.ToString(),
                Status = CommandStatus.Executed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Cleared}
            }
        };

        _unitOfWork.GetCommandsByPaymentRequestId(Arg.Any<Guid>(), default)
            .Returns(commands);

        var result = await GetServiceWithRealMapper().CalculatePaymentRequestStatus(paymentRequestId, default);
        result.ShouldBeEquivalentTo(PaymentRequestStatus.Cancelled);
    }

    [Fact]
    public async Task CalculatePaymentRequestStatus_Returns_Processing_Due_To_Not_Handled_Case()
    {
        var paymentRequestId = Guid.NewGuid();
        var templateId = _templateOptionsMock.Value.Templates.First().FlowTemplateCode!;

        var commands = new List<PaymentRequestCommandEntity>
        {
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.PullFromCustomer.ToString(),
                Status = CommandStatus.Failed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Processed}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.CollectToFunding.ToString(),
                Status = CommandStatus.Placed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Placed}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.CollectToRevenue.ToString(),
                Status = CommandStatus.Placed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Placed}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.PushToMerchant.ToString(),
                Status = CommandStatus.Placed,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Placed}
            }
        };

        _unitOfWork.GetCommandsByPaymentRequestId(Arg.Any<Guid>(), default)
            .Returns(commands);

        var result = await GetServiceWithRealMapper().CalculatePaymentRequestStatus(paymentRequestId, default);
        result.ShouldBeEquivalentTo(PaymentRequestStatus.Processing);
    }

    [Theory]
    [InlineData(TransactionStatus.Failed)]
    [InlineData(TransactionStatus.Error)]
    public async Task CalculatePaymentRequestStatus_Returns_Failed_Due_To_Error_At_Customer_Command(TransactionStatus transactionFailedStatus)
    {
        var paymentRequestId = Guid.NewGuid();
        var templateId = _templateOptionsMock.Value.Templates.First().FlowTemplateCode!;

        var commands = new List<PaymentRequestCommandEntity>
        {
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.PullFromCustomer.ToString(),
                Status = CommandStatus.Failed,
                Transaction = new PaymentTransactionEntity {Status = transactionFailedStatus}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.CollectToFunding.ToString(),
                Status = CommandStatus.Aborted,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Aborted}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.CollectToRevenue.ToString(),
                Status = CommandStatus.Aborted,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Aborted}
            },
            new()
            {
                PaymentRequest = new PaymentRequestEntity {FlowTemplateCode = templateId},
                StepName = StepName.PushToMerchant.ToString(),
                Status = CommandStatus.Aborted,
                Transaction = new PaymentTransactionEntity {Status = TransactionStatus.Aborted}
            }
        };

        _unitOfWork.GetCommandsByPaymentRequestId(Arg.Any<Guid>(), default)
            .Returns(commands);

        var result = await GetServiceWithRealMapper().CalculatePaymentRequestStatus(paymentRequestId, default);
        result.ShouldBeEquivalentTo(PaymentRequestStatus.Failed);
    }

    [Theory, AutoFixtureCustom]
    public async Task StartRollBack_NoRecalledTransactions_SkipsExecution(List<PaymentRequestCommandEntity> commands)
    {
        commands.ForEach(x => x.Status = CommandStatus.Placed);

        await _paymentRequestService.StartRollBack(commands, string.Empty, default);

        await _templatesEngine.Received(TimesCalled.Never).PreGenerateRollbackTransactions(commands);
        await _operationSyncMessageSender.Received(TimesCalled.Never).SendMessage(Arg.Any<ServiceBusMessageBt<SyncOperationMessagePayload>>(), ct: default);
    }

    [Theory, AutoFixtureCustom]
    public async Task StartRollBack_HasPendingCommands_SkipsExecution(List<PaymentRequestCommandEntity> commands)
    {
        commands.ForEach(x => x.Status = CommandStatus.Placed);
        commands.Last().Status = CommandStatus.Pending;

        await _paymentRequestService.StartRollBack(commands, string.Empty, default);

        await _templatesEngine.Received(TimesCalled.Never).PreGenerateRollbackTransactions(commands);
        await _operationSyncMessageSender.Received(TimesCalled.Never).SendMessage(Arg.Any<ServiceBusMessageBt<SyncOperationMessagePayload>>(), ct: default);
    }

    [Theory, AutoFixtureCustom]
    public async Task StartRollBack_HasExecutingCommands_SkipsExecution(List<PaymentRequestCommandEntity> commands)
    {
        commands.ForEach(x => x.Status = CommandStatus.Placed);
        commands.Last().Status = CommandStatus.Executing;

        await _paymentRequestService.StartRollBack(commands, string.Empty, default);

        await _templatesEngine.Received(TimesCalled.Never).PreGenerateRollbackTransactions(commands);
        await _operationSyncMessageSender.Received(TimesCalled.Never).SendMessage(Arg.Any<ServiceBusMessageBt<SyncOperationMessagePayload>>(), ct: default);
    }

    [Theory, AutoFixtureCustom]
    public async Task StartRollBack_NoRollBackTransactionsGenerated_SkipsExecution(List<PaymentRequestCommandEntity> commands, PaymentTransactionEntity transaction)
    {
        transaction.Status = TransactionStatus.Recalled;
        commands.ForEach(x => x.Status = CommandStatus.Placed);
        commands.Last().Status = CommandStatus.Failed;
        commands.Last().Transaction = transaction;

        _templatesEngine.PreGenerateRollbackTransactions(commands).Returns(Enumerable.Empty<PreGeneratedRollbackTransactionModel>());

        await _paymentRequestService.StartRollBack(commands, string.Empty, default);

        await _templatesEngine.Received(TimesCalled.Once).PreGenerateRollbackTransactions(commands);
        await _unitOfWork.Received(TimesCalled.Never).HandleRollbackTransactions(Arg.Any<PaymentRequestCommandEntity>(),
            Arg.Any<List<PaymentTransactionEntity>>(), Arg.Any<List<PaymentRequestCommandEntity>>(), default);
        await _operationSyncMessageSender.Received(TimesCalled.Never).SendMessage(Arg.Any<ServiceBusMessageBt<SyncOperationMessagePayload>>(), ct: default);
    }

    [Theory, AutoFixtureCustom]
    public async Task StartRollBack_RollBackTransactionsGenerated_UpdatesRecalledCommand(List<PaymentRequestCommandEntity> commands, PaymentTransactionEntity transaction,
        IEnumerable<PreGeneratedRollbackTransactionModel> generatedRollbackTransactions, PaymentRequestEntity paymentRequest)
    {
        var updatedBy = nameof(PaymentRequestServiceTests);
        transaction.Status = TransactionStatus.Recalled;
        commands.ForEach(x =>
        {
            x.Status = CommandStatus.Placed;
            x.PaymentRequest = paymentRequest;
        });
        commands.Last().Status = CommandStatus.Failed;
        commands.Last().Transaction = transaction;
        _mapper.Map<PaymentTransactionEntity>(Arg.Any<PreGeneratedRollbackTransactionModel>()).Returns(transaction);
        _templatesEngine.PreGenerateRollbackTransactions(commands).Returns(generatedRollbackTransactions);

        await _paymentRequestService.StartRollBack(commands, updatedBy, default);

        commands.Last().Status.ShouldBe(CommandStatus.Executed);
        commands.Last().UpdatedBy.ShouldBe(updatedBy);

        _unitOfWork.Received(4);
        await _unitOfWork.Received(1).SaveAsync(default);
    }

    [Theory, AutoFixtureCustom]
    public async Task StartRollBack_RollBackTransactionsGenerated_UpdatesRecalledCommand_Notify_Merchant_Disable_Pull(
        List<PaymentRequestCommandEntity> commands,
        PaymentTransactionEntity transaction,
        IEnumerable<PreGeneratedRollbackTransactionModel> generatedRollbackTransactions,
        PaymentRequestEntity paymentRequest)
    {
        var paymetRequestId = Guid.NewGuid();
        var updatedBy = nameof(PaymentRequestServiceTests);
        transaction.Status = TransactionStatus.Recalled;
        commands.ForEach(x =>
        {
            x.Status = CommandStatus.Placed;
            x.PaymentRequest = paymentRequest;
            x.PaymentRequestId = paymetRequestId;
        });
        commands.Last().Status = CommandStatus.Failed;
        commands.Last().Transaction = transaction;

        generatedRollbackTransactions = generatedRollbackTransactions.ToList();
        generatedRollbackTransactions.First().StepName = StepName.PullFromMerchant.ToString();
        generatedRollbackTransactions.First().Status = TransactionStatus.Placed;

        _mapper.Map<PaymentTransactionEntity>(Arg.Any<PreGeneratedRollbackTransactionModel>()).Returns(transaction);
        _templatesEngine.PreGenerateRollbackTransactions(commands).Returns(generatedRollbackTransactions);

        _companyHttpClient.GetCompanyByIdAsync(Arg.Any<string>(), default)
            .Returns(new CompanyModel()
            {
                MerchantAutomaticPullAllowed = false
            });

        await _paymentRequestService.StartRollBack(commands, updatedBy, default);

        commands.Last().Status.ShouldBe(CommandStatus.Executed);
        commands.Last().UpdatedBy.ShouldBe(updatedBy);
        await _notificationService.Received(1).MerchantManualAchPullRequired(Arg.Any<Guid>(), default, Arg.Any<string>());

        _unitOfWork.Received(4);
        await _unitOfWork.Received(1).SaveAsync(default);
    }

    [Theory, AutoFixtureCustom]
    public async Task StartRollBack_RollBackTransactionsGenerated_GeneratesRollbackCommands(List<PaymentRequestCommandEntity> commands, PaymentTransactionEntity transaction,
        PreGeneratedRollbackTransactionModel generatedRollbackTransaction, PaymentRequestEntity paymentRequest)
    {
        var updatedBy = nameof(PaymentRequestServiceTests);
        transaction.Status = TransactionStatus.Recalled;
        commands.ForEach(x =>
        {
            x.Status = CommandStatus.Placed;
            x.PaymentRequest = paymentRequest;
        });
        commands.Last().Status = CommandStatus.Failed;
        commands.Last().Transaction = transaction;
        _mapper.Map<PaymentTransactionEntity>(Arg.Any<PreGeneratedRollbackTransactionModel>()).Returns(transaction);
        _templatesEngine.PreGenerateRollbackTransactions(commands).Returns(new List<PreGeneratedRollbackTransactionModel>() { generatedRollbackTransaction });

        await _paymentRequestService.StartRollBack(commands, updatedBy, default);

        _unitOfWork.Received(4);
        await _unitOfWork.Received(1).SaveAsync(default);
    }

    [Theory, AutoFixtureCustom]
    public async Task StartRollBack_NoCommitChangesRequired_DoesNotSaveChanges(List<PaymentRequestCommandEntity> commands, PaymentTransactionEntity transaction,
        PreGeneratedRollbackTransactionModel generatedRollbackTransaction, PaymentRequestEntity paymentRequest)
    {
        var updatedBy = nameof(PaymentRequestServiceTests);
        transaction.Status = TransactionStatus.Recalled;
        commands.ForEach(x =>
        {
            x.Status = CommandStatus.Placed;
            x.PaymentRequest = paymentRequest;
        });
        commands.Last().Status = CommandStatus.Failed;
        commands.Last().Transaction = transaction;
        _mapper.Map<PaymentTransactionEntity>(Arg.Any<PreGeneratedRollbackTransactionModel>()).Returns(transaction);
        _templatesEngine.PreGenerateRollbackTransactions(commands).Returns(new List<PreGeneratedRollbackTransactionModel>() { generatedRollbackTransaction });

        await _paymentRequestService.StartRollBack(commands, updatedBy, default);

        _unitOfWork.Received(4);
        await _unitOfWork.Received(1).SaveAsync(default);
    }

    [Theory, AutoFixtureCustom]
    public async Task StartRollBackById_RollBackTransactionsGenerated_GeneratesRollbackCommands(List<PaymentRequestCommandEntity> commands, PaymentTransactionEntity transaction,
        PreGeneratedRollbackTransactionModel generatedRollbackTransaction, PaymentRequestEntity paymentRequest)
    {
        var updatedBy = nameof(PaymentRequestServiceTests);
        transaction.Status = TransactionStatus.Recalled;
        commands.ForEach(x =>
        {
            x.Status = CommandStatus.Placed;
            x.PaymentRequest = paymentRequest;
        });
        commands.Last().Status = CommandStatus.Failed;
        commands.Last().Transaction = transaction;

        _unitOfWork
            .Get(default, Arg.Any<Expression<Func<PaymentRequestCommandEntity, bool>>?>(), null,
                $"{nameof(PaymentRequestCommandEntity.Transaction)},{nameof(PaymentRequestCommandEntity.PaymentRequest)}")
            .Returns(commands);
        _mapper.Map<PaymentTransactionEntity>(Arg.Any<PreGeneratedRollbackTransactionModel>()).Returns(transaction);
        _templatesEngine.PreGenerateRollbackTransactions(commands).Returns(new List<PreGeneratedRollbackTransactionModel>() { generatedRollbackTransaction });

        await _paymentRequestService.StartRollBack(commands, updatedBy, default);

        _unitOfWork.Received(4);
        await _unitOfWork.Received(1).SaveAsync(default);
    }

    [Fact]
    public async Task MarkCommandAsExecuted_Execute_Success()
    {
        var commandId = _fixture.Create<Guid>();
        var updatedBy = _fixture.Create<string>();

        var currentCommand = _fixture.Create<PaymentRequestCommandEntity>();
        currentCommand.Id = commandId;
        currentCommand.Status = CommandStatus.Placed;

        var transHistoryPayload = _fixture.Create<PaymentTransactionPayload>();
        var paymentRequest = _fixture.Create<PaymentRequestEntity>();
        currentCommand.PaymentRequest = paymentRequest;
        paymentRequest.Id = currentCommand.PaymentRequestId;
        currentCommand.PaymentRequest.FlowTemplateCode = _templateOptionsMock.Value.Templates[0].FlowTemplateCode!;

        var allCommands = _fixture.CreateMany<PaymentRequestCommandModel>(4).ToList();
        allCommands[0].PaymentRequest!.Status = PaymentRequestStatus.Cancelled;

        foreach (var command in allCommands)
        {
            command.PaymentRequest!.FlowTemplateCode = _templateOptionsMock.Value.Templates[0].FlowTemplateCode!;
        }

        _unitOfWork.GetById<PaymentRequestCommandEntity>
                (Arg.Any<Guid>(), default, Arg.Any<string>())
            .Returns(currentCommand);

        _paymentTransactionHistoryService.BuildPaymentTransactionHistoryPayload
            (Arg.Any<PaymentTransactionEntity>(), Arg.Any<TransactionStatus>(), Arg.Any<string>())
            .Returns(transHistoryPayload);

        _unitOfWork.GetById<PaymentRequestEntity>(Arg.Any<Guid>(), default)
            .Returns(paymentRequest);

        _paymentRequestCommandService.GetByPaymentRequestId(Arg.Any<Guid>(), default)
            .Returns(allCommands);

        await _paymentRequestService.MarkCommandAsExecuted(commandId, updatedBy, default);

        _unitOfWork.ReceivedCalls().Count().ShouldBe(4);
    }

    [Theory, AutoFixtureCustom]
    public async Task ApprovePaymentRequest_EntityNotFound_ThrowsVariableNullException(Guid guid, string confirmedBy)
    {
        _unitOfWork.GetById<PaymentRequestEntity>(Arg.Any<Guid>(), Arg.Any<CancellationToken>()).Returns((PaymentRequestEntity?)null); await Assert.ThrowsAsync<VariableNullException>(async () =>
            await _paymentRequestService.ApprovePaymentRequest(guid, confirmedBy, new PaymentApprovalRequest() { PaymentMethod = PaymentMethod.Ach }, default));
    }

    [Theory, AutoFixtureCustom]
    public async Task ApprovePaymentRequest_ConfirmationTypeIsNone_ThrowsPaymentValidationException(Guid guid, string confirmedBy, PaymentRequestEntity paymentRequest)
    {
        paymentRequest.ConfirmationType = ConfirmationType.None;

        _unitOfWork.GetById<PaymentRequestEntity>(
            Arg.Any<Guid>(),
            Arg.Any<CancellationToken>(),
            Arg.Is($"{nameof(PaymentRequestEntity.Transactions)},{nameof(PaymentRequestEntity.PaymentRequestCommands)}")
        ).Returns(paymentRequest);

        await Assert.ThrowsAsync<PaymentValidationException>(async () =>
            await _paymentRequestService.ApprovePaymentRequest(guid, confirmedBy, new PaymentApprovalRequest() { PaymentMethod = PaymentMethod.Ach }, default));
    }

    [Theory, AutoFixtureCustom]
    public async Task ApprovePaymentRequest_WithManualConfirmation_CompletesWithoutException(Guid guid, string confirmedBy, PaymentRequestEntity paymentRequest)
    {
        paymentRequest.ConfirmationType = ConfirmationType.Manual;

        _unitOfWork.GetById<PaymentRequestEntity>(
            Arg.Any<Guid>(),
            Arg.Any<CancellationToken>(),
            Arg.Is($"{nameof(PaymentRequestEntity.Transactions)},{nameof(PaymentRequestEntity.PaymentRequestCommands)}")
            ).Returns(paymentRequest);
        _unitOfWork.UpdatePaymentRequest(Arg.Any<PaymentRequestEntity>(), Arg.Any<CancellationToken>())
            .Returns(Task.CompletedTask);

        Exception exception = null;

        try
        {
            await _paymentRequestService.ApprovePaymentRequest(guid, confirmedBy, new PaymentApprovalRequest() { PaymentMethod = PaymentMethod.Ach }, default);
        }
        catch (Exception ex)
        {
            exception = ex;
        }

        Assert.Null(exception);
    }

    [Theory]
    [InlineData(PaymentMethod.Wire, PaymentTransactionType.WirePush)]
    [InlineData(PaymentMethod.Instant, PaymentTransactionType.InstantPush)]
    [InlineData(PaymentMethod.Ach, PaymentTransactionType.AchPush)]
    [InlineData(PaymentMethod.SameDayAch, PaymentTransactionType.AchPush)]
    public async Task ApprovePaymentRequest_WithChangedPaymentMethod_UpdatesCorrectTransactionType(
        PaymentMethod newPaymentMethod, PaymentTransactionType expectedPaymentTransactionType)
    {
        // Arrange
        var guid = Guid.NewGuid();
        var confirmedBy = "TestUser";
        var paymentRequest = new PaymentRequestEntity
        {
            Id = guid,
            ConfirmationType = ConfirmationType.Manual,
            RequestType = PaymentRequestType.FinalPaymentV2, // One of the allowed types
            PaymentMethod = PaymentMethod.Ach // Original payment method
        };

        // Create multiple push transactions with different sequence numbers and creation dates
        var transactions = new List<PaymentTransactionEntity>
        {
            new PaymentTransactionEntity {
                Id = Guid.NewGuid(),
                SequenceNumber = 3,
                TransactionType = PaymentTransactionType.AchPush,
                CreatedAt = DateTime.UtcNow.AddHours(-3)
            },
            new PaymentTransactionEntity {
                Id = Guid.NewGuid(),
                SequenceNumber = 4,
                TransactionType = PaymentTransactionType.AchPush, // This should be selected (highest sequence number)
                CreatedAt = DateTime.UtcNow.AddHours(-2)
            },
            new PaymentTransactionEntity {
                Id = Guid.NewGuid(),
                SequenceNumber = 2,
                TransactionType = PaymentTransactionType.AchPull, // This shouldn't be selected (wrong type)
                CreatedAt = DateTime.UtcNow.AddHours(-4)
            },
            new PaymentTransactionEntity {
                Id = Guid.NewGuid(),
                SequenceNumber = 4,
                TransactionType = PaymentTransactionType.AchPush, // Same sequence, earlier created date
                CreatedAt = DateTime.UtcNow.AddHours(-5)
            }
        };

        paymentRequest.Transactions = transactions;

        _unitOfWork.GetById<PaymentRequestEntity>(
            Arg.Is(guid),
            Arg.Any<CancellationToken>(),
            Arg.Is($"{nameof(PaymentRequestEntity.Transactions)},{nameof(PaymentRequestEntity.PaymentRequestCommands)}")
        ).Returns(paymentRequest);

        _unitOfWork.UpdatePaymentRequest(Arg.Any<PaymentRequestEntity>(), Arg.Any<CancellationToken>())
            .Returns(Task.CompletedTask);

        // Act
        await _paymentRequestService.ApprovePaymentRequest(
            guid,
            confirmedBy,
            new PaymentApprovalRequest { PaymentMethod = newPaymentMethod },
            CancellationToken.None
        );

        // Assert
        // Verify the payment method was updated
        Assert.Equal(newPaymentMethod, paymentRequest.PaymentMethod);

        // Find the transaction that should have been updated (highest sequence, then most recent creation date)
        var updatedTransaction = transactions
            .Where(x => x.TransactionType.IsPushTransaction())
            .OrderByDescending(x => x.SequenceNumber)
            .ThenByDescending(x => x.CreatedAt)
            .FirstOrDefault();

        Assert.NotNull(updatedTransaction);
        Assert.Equal(expectedPaymentTransactionType, updatedTransaction.TransactionType);

        // Verify the service called UpdatePaymentRequest
        await _unitOfWork.Received(1).UpdatePaymentRequest(paymentRequest, Arg.Any<CancellationToken>());
    }

    [Theory, AutoFixtureCustom]
    public async Task ApprovePaymentRequest_WithChangedPaymentMethod_NoOutgoingTransaction_OnlyUpdatesPaymentMethod(
        Guid guid, string confirmedBy)
    {
        // Arrange
        var paymentRequest = new PaymentRequestEntity
        {
            Id = guid,
            ConfirmationType = ConfirmationType.Manual,
            RequestType = PaymentRequestType.FinalPayment, // One of the allowed types
            PaymentMethod = PaymentMethod.Ach // Original payment method
        };

        // Create transactions but none of them are push types
        var transactions = new List<PaymentTransactionEntity>
        {
            new PaymentTransactionEntity {
                Id = Guid.NewGuid(),
                SequenceNumber = 3,
                TransactionType = PaymentTransactionType.AchPull,
            },
            new PaymentTransactionEntity {
                Id = Guid.NewGuid(),
                SequenceNumber = 4,
                TransactionType = PaymentTransactionType.AchInternal,
            }
        };

        paymentRequest.Transactions = transactions;

        _unitOfWork.GetById<PaymentRequestEntity>(
            Arg.Any<Guid>(),
            Arg.Any<CancellationToken>(),
            Arg.Is($"{nameof(PaymentRequestEntity.Transactions)},{nameof(PaymentRequestEntity.PaymentRequestCommands)}")
        ).Returns(paymentRequest);

        _unitOfWork.UpdatePaymentRequest(Arg.Any<PaymentRequestEntity>(), Arg.Any<CancellationToken>())
            .Returns(Task.CompletedTask);

        // Act
        await _paymentRequestService.ApprovePaymentRequest(
            guid,
            confirmedBy,
            new PaymentApprovalRequest { PaymentMethod = PaymentMethod.Wire },
            CancellationToken.None
        );

        // Assert
        // Verify the payment method was updated
        Assert.Equal(PaymentMethod.Wire, paymentRequest.PaymentMethod);

        // Verify that no transaction types were changed
        Assert.Equal(PaymentTransactionType.AchPull, transactions[0].TransactionType);
        Assert.Equal(PaymentTransactionType.AchInternal, transactions[1].TransactionType);

        // Verify the service called UpdatePaymentRequest
        await _unitOfWork.Received(1).UpdatePaymentRequest(paymentRequest, Arg.Any<CancellationToken>());
    }

    [Theory, AutoFixtureCustom]
    public async Task ApprovePaymentRequest_RequestTypeNotAllowed_DoesNotUpdatePaymentMethodOrTransactionType(
        Guid guid, string confirmedBy)
    {
        // Arrange
        var paymentRequest = new PaymentRequestEntity
        {
            Id = guid,
            ConfirmationType = ConfirmationType.Manual,
            RequestType = PaymentRequestType.InvoicePayment, // Not in the allowed list for payment method changes
            PaymentMethod = PaymentMethod.Ach // Original payment method
        };

        // Create push transactions that would be updated if the request type allowed it
        var transactions = new List<PaymentTransactionEntity>
        {
            new PaymentTransactionEntity {
                Id = Guid.NewGuid(),
                SequenceNumber = 3,
                TransactionType = PaymentTransactionType.AchPush,
            }
        };

        paymentRequest.Transactions = transactions;

        _unitOfWork.GetById<PaymentRequestEntity>(
            Arg.Any<Guid>(),
            Arg.Any<CancellationToken>(),
            Arg.Is($"{nameof(PaymentRequestEntity.Transactions)},{nameof(PaymentRequestEntity.PaymentRequestCommands)}")
        ).Returns(paymentRequest);

        _unitOfWork.UpdatePaymentRequest(Arg.Any<PaymentRequestEntity>(), Arg.Any<CancellationToken>())
            .Returns(Task.CompletedTask);

        // Act
        await _paymentRequestService.ApprovePaymentRequest(
            guid,
            confirmedBy,
            new PaymentApprovalRequest { PaymentMethod = PaymentMethod.Wire },
            CancellationToken.None
        );

        // Assert
        // Verify the payment method was NOT updated
        Assert.Equal(PaymentMethod.Ach, paymentRequest.PaymentMethod);

        // Verify that transaction type was not changed
        Assert.Equal(PaymentTransactionType.AchPush, transactions[0].TransactionType);

        // Verify the service called UpdatePaymentRequest
        await _unitOfWork.Received(1).UpdatePaymentRequest(paymentRequest, Arg.Any<CancellationToken>());
    }

    [Theory, AutoFixtureCustom]
    public async Task CancelPaymentRequestByDrawId_NoPaymentRequests_ReturnsEmptyList(Guid id, string updatedBy, CancellationToken ct)
    {
        // Arrange
        _paymentRequestRepository.GetByFilter(Arg.Any<PaymentRequestFilter>(), ct)
            .Returns(new PaginatedEntityResult<PaymentRequestEntity>
            {
                Result = new List<PaymentRequestEntity>()
            });

        // Act
        var result = await _paymentRequestService.CancelPaymentRequestByDrawId(id, updatedBy, ct);

        // Assert
        Assert.Empty(result);
    }

    [Theory, AutoFixtureCustom]
    public async Task CancelPaymentRequestByDrawId_PaymentRequestsWithNonTargetRequestTypes_ReturnsEmptyList(Guid id, string updatedBy, CancellationToken ct)
    {
        // Arrange
        var nonTargetPaymentRequests = new List<PaymentRequestEntity>();

        _paymentRequestRepository.GetByFilter(Arg.Any<PaymentRequestFilter>(), ct)
            .Returns(new PaginatedEntityResult<PaymentRequestEntity>
            {
                Result = nonTargetPaymentRequests
            });

        // Act
        var result = await _paymentRequestService.CancelPaymentRequestByDrawId(id, updatedBy, ct);

        // Assert
        Assert.Empty(result);
    }

    [Theory, AutoFixtureCustom]
    public async Task CancelPaymentRequestByDrawId_PaymentRequestsWithTargetRequestTypes_CancelsAndReturnsModels(Guid id, string updatedBy, PaymentRequestEntity paymentRequest1, PaymentRequestEntity paymentRequest2, CancellationToken ct)
    {
        paymentRequest1.RequestType = PaymentRequestType.DrawDisbursement;
        paymentRequest2.RequestType = PaymentRequestType.DrawDisbursement;
        // Arrange
        var paymentRequestEntities = new List<PaymentRequestEntity>
        {
            paymentRequest1,
            paymentRequest2
        };

        _paymentRequestRepository.GetByFilter(Arg.Any<PaymentRequestFilter>(), ct)
            .Returns(new PaginatedEntityResult<PaymentRequestEntity> { Result = paymentRequestEntities });

        foreach (var paymentRequest in paymentRequestEntities)
        {
            _paymentRequestRepository.GetById(paymentRequest.Id, ct).Returns(paymentRequest);
        }

        _mapper.Map<PaymentRequestModel>(Arg.Any<PaymentRequestEntity>())
            .Returns(new PaymentRequestModel());

        // Act
        var result = await _paymentRequestService.CancelPaymentRequestByDrawId(id, updatedBy, ct);

        // Assert
        Assert.Equal(2, result.Count);

        await _unitOfWork.Received(2).SaveAsync(ct);
    }
}
