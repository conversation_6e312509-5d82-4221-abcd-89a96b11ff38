﻿using AutoMapper;
using BlueTape.InvoiceClient.Abstractions;
using BlueTape.PaymentService.Application.Abstractions.Senders;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Abstractions.Services.External.Ledger;
using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;
using BlueTape.PaymentService.Application.Abstractions.Validators;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.InvoicePayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.InvoicePaymentCard;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Abstractions;
using BlueTape.PaymentService.UnitOfWork.Abstractions;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.Application.Services.PaymentRequestCreation;

public class InvoicePaymentCardRequestCreationService(
    IMapper mapper,
    IUnitOfWork unitOfWork,
    IPaymentFlowTemplatesEngine templatesEngine,
    IInvoiceHttpClient invoiceHttpClient,
    IPaymentRequestValidator paymentRequestValidator,
    ILogger<InvoicePaymentV2RequestCreationService> logger,
    IOperationSyncMessageSender operationSyncMessageSender,
    IPaymentTransactionHistoryRepository paymentTransactionHistoryRepository,
    ILedgerService ledgerService,
    INotificationMessageSender notificationMessageSender,
    IPaymentRequestPayableService paymentRequestPayableService,
    ILoanManagementService loanManagementService) : InvoicePaymentRequestCreationService(
    mapper,
    unitOfWork,
    templatesEngine,
    invoiceHttpClient,
    paymentRequestValidator,
    logger,
    operationSyncMessageSender,
    paymentTransactionHistoryRepository,
    ledgerService,
    notificationMessageSender,
    paymentRequestPayableService,
    loanManagementService), IInvoicePaymentCardRequestCreationService
{
    public Task<PaymentRequestModel> Add(InvoicePaymentCardRequestMessage paymentRequestMessage, string createdBy, CancellationToken ct)
    {
        return Add((InvoicePaymentRequestMessage)paymentRequestMessage, createdBy, ct);
    }
}
