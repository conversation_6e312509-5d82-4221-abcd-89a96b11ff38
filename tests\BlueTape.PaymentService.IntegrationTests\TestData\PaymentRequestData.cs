﻿using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.PaymentService.API.ViewModels;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.BaseDisbursement;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawDisbursement;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepaymentCard;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepaymentManual;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.FactoringDisbursement;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.FinalPayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.InvoicePayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.SubscriptionFeePayment;
using BlueTape.PaymentService.IntegrationTests.TestConstants;
using MongoDB.Bson;
using System.Globalization;
using CustomerDetails = BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.CustomerDetails;

namespace BlueTape.PaymentService.IntegrationTests.TestData;

public static class PaymentRequestData
{
    public static BasePaymentRequestMessage CreateValidPaymentRequest(string templateCode, double amount = 11.00, string paymentMethod = "Ach")
    {
        return templateCode switch
        {
            DomainConstants.InvoicePayment => CreateValidInvoicePaymentMessage(amount, paymentMethod: paymentMethod),
            DomainConstants.InvoicePaymentCard => CreateValidInvoicePaymentCardMessage(amount, paymentMethod: paymentMethod),
            DomainConstants.DrawRepayment => CreateValidDrawRepaymentMessage(amount, paymentMethod: paymentMethod),
            DomainConstants.DrawRepaymentCard => CreateValidDrawRepaymentCardMessage(amount, paymentMethod: paymentMethod),
            DomainConstants.InvoicePaymentV2 => CreateValidInvoicePaymentV2Message(amount, paymentMethod: paymentMethod),
            DomainConstants.FactoringFinalPayment => CreateValidFactoringFinalPaymentMessage(amount, paymentMethod: paymentMethod),
            DomainConstants.FinalPayment => CreateValidFinalPaymentMessage(amount, paymentMethod: paymentMethod),
            DomainConstants.FinalPaymentV2 => CreateValidFinalPaymentV2Message(amount, paymentMethod: paymentMethod),
            DomainConstants.InvoiceDisbursementV2 => CreateValidDisbursementV2Message(amount, paymentMethod: paymentMethod),
            DomainConstants.FactoringDisbursement => CreateValidFactoringDisbursementMessage(amount, paymentMethod: paymentMethod),
            DomainConstants.DrawDisbursement => CreateValidDrawDisbursementMessage(amount, paymentMethod: paymentMethod),
            DomainConstants.DrawRepaymentManual => CreateValidDrawRepaymentManualMessage(amount, paymentMethod: paymentMethod),
            DomainConstants.ManualPaymentPull => CreateValidManualPaymentPullMessage(amount, paymentMethod: paymentMethod),
            DomainConstants.SubscriptionFeePayment => CreateValidSubscriptionFeePaymentMessage(amount, paymentMethod: paymentMethod),
            _ => CreateValidInvoicePaymentMessage(amount, paymentMethod: paymentMethod),
        };
    }

    public static SubscriptionFeePaymentRequestMessage CreateValidSubscriptionFeePaymentMessage(double amount,
        string paymentMethod)
    {
        return new SubscriptionFeePaymentRequestMessage
        {
            FlowTemplateCode = DomainConstants.SubscriptionFeePayment,
            BlueTapeCorrelationId = "657056ddff868bcdb89bb8f1",
            CreatedBy = "BlueTape",
            PaymentRequestDetails = new SubscriptionFeePaymentRequestDetails
            {
                Date = DateTime.Parse("2023-12-12", CultureInfo.InvariantCulture),
                Currency = "USD",
                RequestedAmount = new decimal(amount),
                PaymentMethod = paymentMethod,
                ConfirmationType = ConfirmationType.None,
                CustomerDetails = new CustomerDetails
                {
                    Id = Configuration.CustomerCompanyIdWithValidBankAccount,
                    Name = "Deluxe Builder",
                    AccountId = Configuration.ValidBankAccountId,
                },
                AdditionalDetails = new AdditionalDetails
                {
                    Reason = "Subscription fee payment",
                    InvoiceNumber = $"INV-{DateTime.Now:yyyyMMdd}-11111"
                }
            }
        };
    }

    public static InvoicePaymentRequestMessage CreateValidDisbursementV2Message(double amount = 11.00, double feeAmount = 3.82, string? payeeId = null, string? invoiceId = null,
        string paymentMethod = "Ach")
    {
        return new InvoicePaymentRequestMessage
        {
            FlowTemplateCode = DomainConstants.InvoiceDisbursementV2,
            BlueTapeCorrelationId = "657056ddff868bcdb89bb8f1",
            CreatedBy = "BlueTape",
            PaymentRequestDetails = new InvoicePaymentRequestDetails
            {
                Date = DateTime.Parse("2023-12-12T00:00:00.000Z"),
                Currency = "USD",
                RequestedAmount = new decimal(amount),
                PaymentMethod = paymentMethod,
                PayablesDetails = new List<PayablesDetail>
                {
                    new()
                    {
                        Id = (invoiceId ?? ObjectId.GenerateNewId().ToString()) ?? string.Empty,
                        PayableType = "Invoice",
                        PayableAmount = new decimal(amount),
                        RequestedAmount = new decimal(amount)
                    },
                },
                CustomerDetails = new CustomerDetails
                {
                    Id = Configuration.CustomerCompanyIdWithValidBankAccount,
                    Name = "Deluxe Builder",
                    AccountId = Configuration.ValidBankAccountId,
                },
                SellerDetails = new SellerDetails
                {
                    CompanyId = payeeId is null ? Configuration.MerchantCompanyId : payeeId,
                    Name = "Acme Lumber",
                    PaymentSettings = new PaymentSettings
                    {
                        MerchantAchDelayDays = 0
                    }
                },
                FeeDetails = new List<FeeDetails>
                {
                    new()
                    {
                        CompanyId = payeeId is null ? Configuration.MerchantCompanyId : payeeId,
                        Amount = new decimal(feeAmount),
                        Description = "Merchant fee",
                        Type = "Merchant"
                    }
                },
                ProjectDetails = new ProjectDetails
                {
                    Id = "project_id"
                }
            }
        };
    }

    public static InvoicePaymentRequestMessage CreateValidInvoicePaymentMessage(double amount = 11.00, double feeAmount = 3.82, string? payeeId = null, string? invoiceId = null,
        string paymentMethod = "Ach")
    {
        return new InvoicePaymentRequestMessage
        {
            FlowTemplateCode = DomainConstants.InvoicePayment,
            BlueTapeCorrelationId = "657056ddff868bcdb89bb8f1",
            CreatedBy = "BlueTape",
            PaymentRequestDetails = new InvoicePaymentRequestDetails
            {
                Date = DateTime.Parse("2023-12-12T00:00:00.000Z"),
                Currency = "USD",
                RequestedAmount = new decimal(amount),
                PaymentMethod = paymentMethod,
                PayablesDetails = new List<PayablesDetail>
                {
                    new()
                    {
                        Id = (invoiceId ?? ObjectId.GenerateNewId().ToString()) ?? string.Empty,
                        PayableType = "Invoice",
                        PayableAmount = new decimal(amount),
                        RequestedAmount = new decimal(amount)
                    },
                },
                CustomerDetails = new CustomerDetails
                {
                    Id = Configuration.CustomerCompanyIdWithValidBankAccount,
                    Name = "Deluxe Builder",
                    AccountId = Configuration.ValidBankAccountId,
                },
                SellerDetails = new SellerDetails
                {
                    CompanyId = payeeId is null ? Configuration.MerchantCompanyId : payeeId,
                    Name = "Acme Lumber",
                    PaymentSettings = new PaymentSettings
                    {
                        MerchantAchDelayDays = 0
                    }
                },
                FeeDetails = new List<FeeDetails>
                {
                    new()
                    {
                        CompanyId = payeeId is null ? Configuration.MerchantCompanyId : payeeId,
                        Amount = new decimal(feeAmount),
                        Description = "Merchant fee",
                        Type = "Merchant"
                    }
                },
                ProjectDetails = new ProjectDetails
                {
                    Id = "project_id"
                }
            }
        };
    }

    public static InvoicePaymentRequestMessage CreateValidMultipleInvoicePaymentMessage(double amount = 20.00, double feeAmount = 3.82, string? payeeId = null, string? invoiceId = null,
    string paymentMethod = "Ach")
    {
        var negativeAmountForSecondInvoice = -(amount * 0.25);
        var totalAmount = negativeAmountForSecondInvoice + amount;

        return new InvoicePaymentRequestMessage
        {
            FlowTemplateCode = DomainConstants.InvoicePayment,
            BlueTapeCorrelationId = "657056ddff868bcdb89bb8f1",
            CreatedBy = "BlueTape",
            PaymentRequestDetails = new InvoicePaymentRequestDetails
            {
                Date = DateTime.Parse("2023-12-12T00:00:00.000Z"),
                Currency = "USD",
                RequestedAmount = (decimal)totalAmount,
                PaymentMethod = paymentMethod,
                PayablesDetails = new List<PayablesDetail>
                {
                    new()
                    {
                        Id = (invoiceId ?? ObjectId.GenerateNewId().ToString()) ?? string.Empty,
                        PayableType = "Invoice",
                        PayableAmount = (decimal)amount,
                        RequestedAmount = (decimal)amount
                    },
                    new()
                    {
                        Id = (invoiceId ?? ObjectId.GenerateNewId().ToString()) ?? string.Empty,
                        PayableType = "Invoice",
                        PayableAmount = (decimal)negativeAmountForSecondInvoice,
                        RequestedAmount = (decimal)negativeAmountForSecondInvoice
                    },
                },
                CustomerDetails = new CustomerDetails
                {
                    Id = Configuration.CustomerCompanyIdWithValidBankAccount,
                    Name = "Deluxe Builder",
                    AccountId = Configuration.ValidBankAccountId,
                },
                SellerDetails = new SellerDetails
                {
                    CompanyId = payeeId is null ? Configuration.MerchantCompanyId : payeeId,
                    Name = "Acme Lumber",
                    PaymentSettings = new PaymentSettings
                    {
                        MerchantAchDelayDays = 0
                    }
                },
                FeeDetails = new List<FeeDetails>
                {
                    new()
                    {
                        CompanyId = payeeId is null ? Configuration.MerchantCompanyId : payeeId,
                        Amount = new decimal(feeAmount),
                        Description = "Merchant fee",
                        Type = "Merchant"
                    }
                },
                ProjectDetails = new ProjectDetails
                {
                    Id = "project_id"
                }
            }
        };
    }

    public static InvoicePaymentRequestMessage CreateValidInvoicePaymentV2Message(double amount = 11.00, double feeAmount = 3.82, string? payeeId = null, string? invoiceId = null,
    string paymentMethod = "Ach")
    {
        return new InvoicePaymentRequestMessage
        {
            FlowTemplateCode = DomainConstants.InvoicePaymentV2,
            BlueTapeCorrelationId = "657056ddff868bcdb89bb8f1",
            CreatedBy = "BlueTape",
            PaymentRequestDetails = new InvoicePaymentRequestDetails
            {
                Date = DateTime.Parse("2023-12-12T00:00:00.000Z"),
                Currency = "USD",
                RequestedAmount = new decimal(amount),
                PaymentMethod = paymentMethod,
                PayablesDetails = new List<PayablesDetail>
                {
                    new()
                    {
                        Id = (invoiceId ?? ObjectId.GenerateNewId().ToString()) ?? string.Empty,
                        PayableType = "Invoice",
                        PayableAmount = new decimal(amount),
                        RequestedAmount = new decimal(amount)
                    },
                },
                CustomerDetails = new CustomerDetails
                {
                    Id = Configuration.CustomerCompanyIdWithValidBankAccount,
                    Name = "Deluxe Builder",
                    AccountId = Configuration.ValidBankAccountId,
                },
                SellerDetails = new SellerDetails
                {
                    CompanyId = payeeId is null ? Configuration.MerchantCompanyId : payeeId,
                    Name = "Acme Lumber",
                    PaymentSettings = new PaymentSettings
                    {
                        MerchantAchDelayDays = 0
                    }
                },
                FeeDetails = new List<FeeDetails>
                {
                    new()
                    {
                        CompanyId = payeeId is null ? Configuration.MerchantCompanyId : payeeId,
                        Amount = new decimal(feeAmount),
                        Description = "Merchant fee",
                        Type = "Merchant"
                    }
                },
                ProjectDetails = new ProjectDetails
                {
                    Id = "project_id"
                }
            }
        };
    }

    public static InvoicePaymentRequestMessage CreateValidInvoicePaymentCardMessage(double amount = 11.00, double feeAmount = 3.82, string? payeeId = null, string? invoiceId = null,
    string paymentMethod = "Ach")
    {
        return new InvoicePaymentRequestMessage
        {
            FlowTemplateCode = DomainConstants.InvoicePaymentCard,
            BlueTapeCorrelationId = "657056ddff868bcdb89bb8f1",
            CreatedBy = "BlueTape",
            PaymentRequestDetails = new InvoicePaymentRequestDetails
            {
                Date = DateTime.Parse("2023-12-12T00:00:00.000Z"),
                Currency = "USD",
                RequestedAmount = new decimal(amount),
                PaymentMethod = paymentMethod,
                PayablesDetails = new List<PayablesDetail>
                {
                    new()
                    {
                        Id = (invoiceId ?? ObjectId.GenerateNewId().ToString()) ?? string.Empty,
                        PayableType = "Invoice",
                        PayableAmount = new decimal(amount),
                        RequestedAmount = new decimal(amount)
                    },
                },
                CustomerDetails = new CustomerDetails
                {
                    Id = Configuration.CustomerCompanyIdWithValidBankAccount,
                    Name = "Deluxe Builder",
                    AccountId = Configuration.ValidBankAccountId,
                },
                SellerDetails = new SellerDetails
                {
                    CompanyId = payeeId is null ? Configuration.MerchantCompanyId : payeeId,
                    Name = "Acme Lumber",
                    PaymentSettings = new PaymentSettings
                    {
                        MerchantAchDelayDays = 0
                    }
                },
                FeeDetails = new List<FeeDetails>
                {
                    new()
                    {
                        CompanyId = payeeId is null ? Configuration.MerchantCompanyId : payeeId,
                        Amount = new decimal(feeAmount),
                        Description = "Merchant fee",
                        Type = "Merchant"
                    }
                },
                ProjectDetails = new ProjectDetails
                {
                    Id = "project_id"
                },
                CardPaymentDetails = new()
                {
                    TransactionId = "dlj807sdjg9725",
                }
            }
        };
    }

    public static DrawRepaymentRequestMessage CreateValidDrawRepaymentMessage(double amount = 12.00, Guid? drawId = null,
    string paymentMethod = "Ach")
    {
        return new DrawRepaymentRequestMessage
        {
            FlowTemplateCode = DomainConstants.DrawRepayment,
            BlueTapeCorrelationId = "657056ddff868bcdb89bb8f1",
            CreatedBy = "BlueTape",
            PaymentRequestDetails = new DrawRepaymentRequestDetails
            {
                Date = DateTime.Parse("2023-12-12T00:00:00.000Z"),
                Currency = "USD",
                RequestedAmount = new decimal(amount),
                PaymentMethod = paymentMethod,
                DrawRepaymentDrawDetails = new DrawRepaymentDrawDetails
                {
                    Id = drawId ?? Configuration.TestDrawId,
                    Amount = new decimal(amount)
                },
                CustomerDetails = new CustomerDetails
                {
                    Id = Configuration.CustomerCompanyIdWithValidBankAccount,
                    Name = "Deluxe Builder",
                    AccountId = Configuration.ValidBankAccountId,
                },
                CompatibilityDetails = new CompatibilityDetails
                {
                    LmsPaymentId = "50111f79-7390-472d-bc23-6d1f2b16d924",
                }
            }
        };
    }

    public static DrawRepaymentCardRequestMessage CreateValidDrawRepaymentCardMessage(double amount = 12.00, Guid? drawId = null,
        string paymentMethod = "Ach")
    {
        return new DrawRepaymentCardRequestMessage
        {
            FlowTemplateCode = DomainConstants.DrawRepaymentCard,
            BlueTapeCorrelationId = "657056ddff868bcdb89bb8f1",
            CreatedBy = "BlueTape",
            PaymentRequestDetails = new DrawRepaymentRequestDetails
            {
                Date = DateTime.Parse("2023-12-12T00:00:00.000Z"),
                Currency = "USD",
                RequestedAmount = new decimal(amount),
                PaymentMethod = paymentMethod,
                DrawRepaymentDrawDetails = new DrawRepaymentDrawDetails
                {
                    Id = drawId ?? Configuration.TestDrawId,
                    Amount = new decimal(amount)
                },
                CustomerDetails = new CustomerDetails
                {
                    Id = Configuration.CustomerCompanyIdWithValidBankAccount,
                    Name = "Deluxe Builder",
                    AccountId = Configuration.ValidBankAccountId,
                },
                CompatibilityDetails = new CompatibilityDetails
                {
                    LmsPaymentId = "50111f79-7390-472d-bc23-6d1f2b16d924",
                },
                CardPaymentDetails = new()
                {
                    TransactionId = "dlj807sdjg9725",
                }
            }
        };
    }

    public static DrawRepaymentManualRequestMessage CreateValidDrawRepaymentManualMessage(double amount = 12.00, Guid? drawId = null,
        string paymentMethod = "Ach")
    {
        return new DrawRepaymentManualRequestMessage
        {
            FlowTemplateCode = DomainConstants.DrawRepaymentManual,
            BlueTapeCorrelationId = "657056ddff868bcdb89bb8f1",
            CreatedBy = "BlueTape",
            PaymentRequestDetails = new()
            {
                Date = DateTime.Parse("2023-12-12T00:00:00.000Z"),
                Currency = "USD",
                RequestedAmount = new decimal(amount),
                PaymentMethod = paymentMethod,
                DrawDetails = new()
                {
                    Id = drawId ?? Configuration.TestDrawId,
                    Amount = new decimal(amount)
                },
                CustomerDetails = new CustomerDetails
                {
                    Id = Configuration.CustomerCompanyIdWithValidBankAccount,
                    Name = "Deluxe Builder",
                    AccountId = Configuration.ValidBankAccountId,
                },
                ManualPaymentDetails = new()
                {
                    ManualPaymentMethod = ManualPaymentMethod.Ach.ToString(),
                    ManualAccountCode = AccountCodeType.LOCKBOXCOLLECTION.ToString(),
                    ExternalReferenceNumber = "TESTREFERENCE",
                    UserId = "TESTUSER",
                },
            }
        };
    }

    public static DrawDisbursementRequestMessage CreateValidDrawDisbursementMessage(double amount = 12.00, Guid? drawId = null,
        string paymentMethod = "Ach", string? payeeId = null)
    {
        return new DrawDisbursementRequestMessage
        {
            FlowTemplateCode = DomainConstants.DrawDisbursement,
            BlueTapeCorrelationId = "657056ddff868bcdb89bb8f1",
            CreatedBy = "BlueTape",
            PaymentRequestDetails = new BaseDisbursementRequestDetails
            {
                Date = DateTime.Parse("2023-12-12T00:00:00.000Z"),
                Currency = "USD",
                RequestedAmount = new decimal(amount),
                PaymentMethod = paymentMethod,
                DrawDetails = new BaseDisbursementDrawDetails
                {
                    Id = drawId ?? Configuration.TestDrawId,
                },
                SellerDetails = new SellerDetails
                {
                    CompanyId = payeeId is null ? Configuration.MerchantCompanyId : payeeId,
                    Name = "Acme Lumber",
                    PaymentSettings = new PaymentSettings
                    {
                        MerchantAchDelayDays = 0
                    }
                },
                PayablesDetails = new List<PayablesDetail>
                {
                    new()
                    {
                        Id = ObjectId.GenerateNewId().ToString(),
                        PayableType = "Invoice",
                        PayableAmount = new decimal(amount),
                        RequestedAmount = new decimal(amount)
                    },
                },
                FeeDetails = new List<FeeDetails>
                {
                    new()
                    {
                        CompanyId = Configuration.MerchantCompanyId,
                        Amount = new decimal(amount * 0.015),
                        Description = "Merchant fee",
                        Type = "Merchant"
                    }
                },
            }
        };
    }

    public static FinalPaymentRequestMessage CreateValidFinalPaymentMessage(double amount = 13.00, Guid? drawId = null,
        string paymentMethod = "Ach")
    {
        return new FinalPaymentRequestMessage
        {
            FlowTemplateCode = DomainConstants.FinalPayment,
            BlueTapeCorrelationId = "657056ddff868bcdb89bb8f1",
            CreatedBy = "BlueTape",
            PaymentRequestDetails = new BaseDisbursementRequestDetails()
            {
                Date = DateTime.Parse("2023-12-12T00:00:00.000Z"),
                Currency = "USD",
                RequestedAmount = new decimal(amount),
                PaymentMethod = paymentMethod,
                DrawDetails = new BaseDisbursementDrawDetails
                {
                    Id = drawId ?? Configuration.TestDrawId,
                },
                SellerDetails = new SellerDetails
                {
                    CompanyId = Configuration.MerchantCompanyId,
                    Name = "Acme Lumber",
                    PaymentSettings = new PaymentSettings
                    {
                        MerchantAchDelayDays = 0
                    }
                },
                PayablesDetails = new List<PayablesDetail>
                {
                    new()
                    {
                        Id = ObjectId.GenerateNewId().ToString(),
                        PayableType = "Invoice",
                        PayableAmount = new decimal(amount),
                        RequestedAmount = new decimal(amount),
                        Discount = new decimal(amount * 0.15)
                    },
                },
                FeeDetails = new List<FeeDetails>
                {
                    new()
                    {
                        CompanyId = Configuration.MerchantCompanyId,
                        Amount = new decimal(amount * 0.015),
                        Description = "Merchant fee",
                        Type = "Merchant"
                    }
                },
            }
        };
    }

    public static FinalPaymentRequestMessage CreateValidFinalPaymentV2Message(double amount = 13.00, Guid? drawId = null,
        string paymentMethod = "Ach")
    {
        return new FinalPaymentRequestMessage
        {
            FlowTemplateCode = DomainConstants.FinalPaymentV2,
            BlueTapeCorrelationId = "657056ddff868bcdb89bb8f1",
            CreatedBy = "BlueTape",
            PaymentRequestDetails = new BaseDisbursementRequestDetails()
            {
                Date = DateTime.Parse("2023-12-12T00:00:00.000Z"),
                Currency = "USD",
                RequestedAmount = new decimal(amount),
                PaymentMethod = paymentMethod,
                DrawDetails = new BaseDisbursementDrawDetails
                {
                    Id = drawId ?? Configuration.TestDrawId,
                },
                SellerDetails = new SellerDetails
                {
                    CompanyId = Configuration.MerchantCompanyId,
                    Name = "Acme Lumber",
                    PaymentSettings = new PaymentSettings
                    {
                        MerchantAchDelayDays = 0
                    }
                },
                PayablesDetails = new List<PayablesDetail>
                {
                    new()
                    {
                        Id = ObjectId.GenerateNewId().ToString(),
                        PayableType = "Invoice",
                        PayableAmount = new decimal(amount),
                        RequestedAmount = new decimal(amount),
                        Discount = new decimal(amount * 0.15)
                    },
                },
                FeeDetails = new List<FeeDetails>
                {
                    new()
                    {
                        CompanyId = Configuration.MerchantCompanyId,
                        Amount = new decimal(amount * 0.015),
                        Description = "Merchant fee",
                        Type = "Merchant"
                    }
                },
            }
        };
    }

    public static FactoringDisbursementRequestMessage CreateValidFactoringDisbursementMessage(double amount = 13.00, Guid? drawId = null, string paymentMethod = "Ach")
    {
        return new FactoringDisbursementRequestMessage
        {
            FlowTemplateCode = DomainConstants.FactoringDisbursement,
            BlueTapeCorrelationId = "657056ddff868bcdb89bb8f1",
            CreatedBy = "BlueTape",
            PaymentRequestDetails = new BaseDisbursementRequestDetails
            {
                Date = DateTime.Parse("2023-12-12T00:00:00.000Z"),
                Currency = "USD",
                RequestedAmount = new decimal(amount),
                PaymentMethod = paymentMethod,
                DrawDetails = new BaseDisbursementDrawDetails
                {
                    Id = drawId ?? Configuration.TestDrawId,
                },
                SellerDetails = new SellerDetails
                {
                    CompanyId = Configuration.MerchantCompanyId,
                    Name = "Acme Lumber",
                    PaymentSettings = new PaymentSettings
                    {
                        MerchantAchDelayDays = 0
                    }
                },
                PayablesDetails = new List<PayablesDetail>
                {
                    new()
                    {
                        Id = ObjectId.GenerateNewId().ToString(),
                        PayableType = "Invoice",
                        PayableAmount = new decimal(amount),
                        RequestedAmount = new decimal(amount)
                    },
                },
                FeeDetails = new List<FeeDetails>
                {
                    new()
                    {
                        CompanyId = Configuration.MerchantCompanyId,
                        Amount = new decimal(amount * 0.015),
                        Description = "Merchant fee",
                        Type = "Merchant"
                    }
                },
            }
        };
    }

    public static FinalPaymentRequestMessage CreateValidFactoringFinalPaymentMessage(double amount = 13.00, Guid? drawId = null,
        string paymentMethod = "Ach")
    {
        return new FinalPaymentRequestMessage
        {
            FlowTemplateCode = DomainConstants.FactoringFinalPayment,
            BlueTapeCorrelationId = "657056ddff868bcdb89bb8f1",
            CreatedBy = "BlueTape",
            PaymentRequestDetails = new BaseDisbursementRequestDetails

            {
                Date = DateTime.Parse("2023-12-12T00:00:00.000Z"),
                Currency = "USD",
                RequestedAmount = new decimal(amount),
                PaymentMethod = paymentMethod,
                DrawDetails = new BaseDisbursementDrawDetails
                {
                    Id = drawId ?? Configuration.TestDrawId,
                },
                SellerDetails = new SellerDetails
                {
                    CompanyId = Configuration.MerchantCompanyId,
                    Name = "Acme Lumber",
                    PaymentSettings = new PaymentSettings
                    {
                        MerchantAchDelayDays = 0
                    }
                },
                PayablesDetails = new List<PayablesDetail>
                {
                    new()
                    {
                        Id = ObjectId.GenerateNewId().ToString(),
                        PayableType = "Invoice",
                        PayableAmount = new decimal(amount),
                        RequestedAmount = new decimal(amount)
                    },
                },
                FeeDetails = new List<FeeDetails>
                {
                    new()
                    {
                        CompanyId = Configuration.MerchantCompanyId,
                        Amount = new decimal(amount * 0.015),
                        Description = "Merchant fee",
                        Type = "Merchant"
                    }
                },
            }
        };
    }

    public static CreatePaymentRequestViewModel GetCreatePaymentRequest(decimal amount = 11m)
    {
        return new CreatePaymentRequestViewModel
        {
            SubjectType = SubjectType.Payable,
            RequestType = PaymentRequestType.InvoicePayment,
            PaymentMethod = PaymentMethod.Card,
            FlowTemplateCode = "CREATE.PAYNOW.INVOICE_PAYMENT",
            PayeeId = "657056ddff868bcdb89bb8f1",
            PayerId = "657056ddff868bcdb89bb8f1",
            SellerId = "657056ddff868bcdb89bb8f1",

            CustomerAccountId = Guid.NewGuid().ToString(),
            CreditId = Guid.NewGuid(),
            Amount = amount,
            FeeAmount = 5,
            Currency = "USD",
            Date = new DateOnly(2000, 1, 1, new JulianCalendar()),



            PaymentRequestPayables = new List<CreatePaymentRequestPayableViewModel>
            {
                new()
                    {
                        Id = ObjectId.GenerateNewId().ToString(),
                        PayableType = PayableType.Invoice,
                        PayableAmount = amount,
                        RequestedAmount = amount,
                        Discount = 0
                    },
            },
            PaymentRequestFees = new List<CreatePaymentRequestFeeViewModel>
            {
                new()
                {
                        CompanyId = Guid.NewGuid(),
                        Amount = 5,
                        Type = FeeType.Merchant
                }
            }
        };
    }

    public static DrawRepaymentManualRequestMessage CreateValidManualPaymentPullMessage(double amount = 12.00, Guid? drawId = null,
        string paymentMethod = "Ach")
    {
        return new DrawRepaymentManualRequestMessage
        {
            FlowTemplateCode = DomainConstants.ManualPaymentPull,
            BlueTapeCorrelationId = "657056ddff868bcdb89bb8f1",
            CreatedBy = "BlueTape",
            PaymentRequestDetails = new()
            {
                Date = DateTime.Parse("2023-12-12T00:00:00.000Z"),
                Currency = "USD",
                RequestedAmount = new decimal(amount),
                PaymentMethod = paymentMethod,
                CustomerDetails = new CustomerDetails
                {
                    Id = Configuration.CustomerCompanyIdWithValidBankAccount,
                    Name = "Deluxe Builder",
                    AccountId = Configuration.ValidBankAccountId,
                },
                ManualPaymentDetails = new()
                {
                    ManualAccountCode = AccountCodeType.REVENUE.ToString(),
                },
            }
        };
    }

    public static CreatePaymentRequestTransactionViewModel CreateValidPaymentRequestTransaction(decimal amount = 11.00m, decimal discount = 0.00m, string? originatorAccountId = null, string? receiverAccountId = null)
    {
        return new CreatePaymentRequestTransactionViewModel
        {
            TransactionType = PaymentTransactionType.AchPull,
            PaymentMethod = PaymentMethod.Card,
            OriginatorAccountId = originatorAccountId,
            ReceiverAccountId = receiverAccountId,
            Amount = amount,
            Discount = discount,
            Currency = "USD",
            Date = new DateOnly(2023, 12, 12),
            TransactionNumber = Guid.NewGuid().ToString(),
            ReferenceNumber = "Reference123",
            MetaData = "Additional metadata",
            Reason = "Reason for transaction"
        };
    }
}
