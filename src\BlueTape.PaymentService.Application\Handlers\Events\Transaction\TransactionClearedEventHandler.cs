﻿using BlueTape.PaymentService.Application.Abstractions.Senders;
using BlueTape.PaymentService.Application.Models.Events.Transaction;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Messages;
using BlueTape.ServiceBusMessaging.Attributes;
using MediatR;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.Application.Handlers.Events.Transaction;

public class TransactionClearedEventHandler(
    IPaymentTransactionRepository paymentTransactionRepository,
    IPaymentTransactionHistoryRepository paymentTransactionHistoryRepository,
    INotificationMessageSender notificationMessageSender,
    ILogger<TransactionClearedEventHandler> logger)
    : BaseTransactionEventHandler(
            paymentTransactionRepository,
            paymentTransactionHistoryRepository,
            logger), IRequestHandler<TransactionClearedEvent>
{
    protected override IEnumerable<TransactionStatus> AllowedTransactionStatus => new[]
    {
        TransactionStatus.Cleared
    };

    public Task Handle(TransactionClearedEvent request, CancellationToken cancellationToken)
        => base.Handle(request, cancellationToken);

    protected override async Task HandleAch(PaymentTransactionEntity transaction, CancellationToken ct)
    {
        if (transaction.PaymentRequest is null)
        {
            logger.LogWarning($"Unable to handle recalled transaction notifications for transactionId: {transaction.Id} due to payment request is null, paymentRequestId: {transaction.PaymentRequestId}");
            return;
        }

        switch (transaction.PaymentRequest.FlowTemplateCode)
        {
            case DomainConstants.DrawRepaymentCard:
            case DomainConstants.DrawRepayment:
                await HandleAchDrawRepayment(transaction, ct);
                break;
            case DomainConstants.InvoicePaymentCard:
            case DomainConstants.InvoicePayment:
            case DomainConstants.InvoicePaymentV2:
            case DomainConstants.InvoiceDisbursementV2:
                await HandleAchPayNow(transaction, ct);
                break;
            case DomainConstants.FactoringFinalPayment:
            case DomainConstants.FinalPayment:
            case DomainConstants.FinalPaymentV2:
                await HandleFinalPayment(transaction, ct);
                break;
            case DomainConstants.FactoringDisbursement:
            case DomainConstants.DrawDisbursement:
                await HandleDisbursement(transaction, ct);
                break;
            default:
                logger.LogWarning($"Unable to handle payment request of type: {transaction.PaymentRequest.FlowTemplateCode}, paymentRequestId: {transaction.PaymentRequestId}");
                break;
        }
    }

    private async Task HandleAchPayNow(PaymentTransactionEntity transaction, CancellationToken ct)
    {
        var transactionHistory = await GetTransactionHistory(transaction.Id, ct);

        var messagesToSend = new ServiceBusMessageBt<NotificationMessagePayloadV2>(new NotificationMessagePayloadV2
        {
            Id = transactionHistory!.Id,
            NotificationType = NotificationType.TransactionHistoryUpdate
        });

        await notificationMessageSender.SendMessage(messagesToSend, ct);
    }

    private async Task HandleDisbursement(PaymentTransactionEntity transaction, CancellationToken ct)
    {
        var transactionHistory = await GetTransactionHistory(transaction.Id, ct);

        var messagesToSend = new ServiceBusMessageBt<NotificationMessagePayloadV2>(new NotificationMessagePayloadV2
        {
            Id = transactionHistory!.Id,
            NotificationType = NotificationType.TransactionHistoryUpdate
        });

        await notificationMessageSender.SendMessage(messagesToSend, ct);
    }

    private async Task HandleFinalPayment(PaymentTransactionEntity transaction, CancellationToken ct)
    {
        var transactionHistory = await GetTransactionHistory(transaction.Id, ct);

        var messagesToSend = new ServiceBusMessageBt<NotificationMessagePayloadV2>(new NotificationMessagePayloadV2
        {
            Id = transactionHistory!.Id,
            NotificationType = NotificationType.TransactionHistoryUpdate
        });

        await notificationMessageSender.SendMessage(messagesToSend, ct);
    }

    private async Task HandleAchDrawRepayment(PaymentTransactionEntity transaction, CancellationToken ct)
    {
        await Task.CompletedTask;
    }
}