﻿using BlueTape.LinqpalClient.Abstractions;
using BlueTape.PaymentService.Application.Abstractions.Processors;
using BlueTape.PaymentService.Application.Abstractions.Services.Notification;
using BlueTape.PaymentService.Application.Services.Notification.RequestTypes;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Exceptions;
using BlueTape.PaymentService.Domain.Messages;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.Application.Processors;

public class NotificationProcessor(
    IPaymentRequestRepository paymentRequestRepository,
    IPaymentTransactionHistoryRepository paymentTransactionHistoryRepository,
    IPaymentTransactionRepository paymentTransactionRepository,
    ITransactionNotificationService transactionNotificationService,
    ILinqpalHttpClient linqpalHttpClient,
    ILoggerFactory loggerFactory) : INotificationProcessor
{
    private readonly ILogger _logger = loggerFactory.CreateLogger<NotificationProcessor>();

    public async Task Execute(NotificationMessagePayloadV2 messagePayloadV2, CancellationToken ct)
    {
        switch (messagePayloadV2.NotificationType)
        {
            case NotificationType.None:
                break;
            case NotificationType.TransactionHistoryUpdate:
                await NotifyTransactionUpdate(messagePayloadV2.Id, ct);
                break;
            case NotificationType.PaymentRequestCreated:
                await NotifyPaymentRequestCreated(messagePayloadV2.Id, ct);
                break;
            default:
                throw new ArgumentOutOfRangeException();
        }
    }

    public async Task NotifyTransactionUpdate(Guid transactionHistoryId, CancellationToken ct)
    {
        var transactionHistory = await paymentTransactionHistoryRepository.GetById(transactionHistoryId, ct);
        if (transactionHistory is null) throw new TransactionHistoryDoesNotExistException(transactionHistoryId);

        var transaction = await paymentTransactionRepository.GetByIdFull(transactionHistory.TransactionId, ct);
        if (transaction is null) throw new TransactionDoesNotExistException(transactionHistory.TransactionId);

        if (transaction.PaymentRequest is null) throw new PaymentRequestDoesNotExistException
            ($"Unable to find payment request for id: {transaction.PaymentRequestId}");

        var paymentRequest = transaction.PaymentRequest;

        using (_logger.BeginScope(new Dictionary<string, object>
               {
                   {"PaymentRequestId", paymentRequest.Id}
               }))
        {
            var notificationService = GetNotifyTransactionUpdateService(paymentRequest.RequestType);

            try
            {
                if (notificationService is null) return;

                await notificationService.ProcessTransactionUpdate(transactionHistoryId, ct);
            }
            catch (TransactionHistoryDuplicatedNotificationException)
            {
                _logger.LogWarning($"Detected attempt to send duplicated notification for transactionHistoryId: {transactionHistoryId.ToString()}");
                throw;
            }
        }
    }

    public async Task NotifyPaymentRequestCreated(Guid paymentRequestId, CancellationToken ct)
    {
        var paymentRequest = await paymentRequestRepository.GetById(paymentRequestId, ct);
        if (paymentRequest is null)
        {
            throw new PaymentRequestDoesNotExistException($"Payment request does not exist with id: {paymentRequestId.ToString()}");
        }

        using (_logger.BeginScope(new Dictionary<string, object>
               {
                   {"PaymentRequestId", paymentRequest.Id}
               }))
        {
            var notificationService = GetNotifyTransactionUpdateService(paymentRequest.RequestType);

            if (notificationService is null) return;

            await notificationService.ProcessPaymentRequestCreated(paymentRequest.Id, ct);
        }
    }

    private IBaseNotificationService? GetNotifyTransactionUpdateService(PaymentRequestType requestType)
    {
        switch (requestType)
        {
            case PaymentRequestType.InvoicePaymentCard:
            case PaymentRequestType.InvoicePayment:
                return new InvoicePaymentNotificationService(
                    paymentTransactionHistoryRepository,
                    paymentTransactionRepository,
                    transactionNotificationService,
                    loggerFactory);
            case PaymentRequestType.DrawRepaymentCard:
            case PaymentRequestType.DrawRepayment:
                return new DrawRepaymentNotificationService(
                    paymentTransactionHistoryRepository,
                    paymentTransactionRepository,
                    transactionNotificationService,
                    linqpalHttpClient,
                    loggerFactory);
            case PaymentRequestType.FinalPayment:
            case PaymentRequestType.FinalPaymentV2:
                return new FinalPaymentNotificationService(
                    paymentTransactionHistoryRepository,
                    paymentTransactionRepository,
                    transactionNotificationService,
                    loggerFactory);
            case PaymentRequestType.InvoicePaymentV2:
                return new InvoicePaymentV2NotificationService(
                    paymentTransactionHistoryRepository,
                    paymentTransactionRepository,
                    transactionNotificationService,
                    loggerFactory);
            case PaymentRequestType.InvoiceDisbursementV2:
                return new InvoiceDisbursementV2NotificationService(
                    paymentTransactionHistoryRepository,
                    paymentTransactionRepository,
                    transactionNotificationService,
                    loggerFactory);
            case PaymentRequestType.FactoringDisbursement:
                return new FactoringDisbursementNotificationService(
                    paymentTransactionHistoryRepository,
                    paymentTransactionRepository,
                    transactionNotificationService,
                    loggerFactory);
            case PaymentRequestType.FactoringFinalPayment:
                return new FactoringFinalPaymentNotificationService(
                    paymentTransactionHistoryRepository,
                    paymentTransactionRepository,
                    transactionNotificationService,
                    loggerFactory);
            case PaymentRequestType.DrawDisbursement:
                return new DrawDisbursementNotificationService(
                    paymentTransactionHistoryRepository,
                    paymentTransactionRepository,
                    transactionNotificationService,
                    loggerFactory);
            default:
                return null;
        }
    }
}
