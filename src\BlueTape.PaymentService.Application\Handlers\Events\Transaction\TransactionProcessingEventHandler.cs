using BlueTape.PaymentService.Application.Abstractions.Senders;
using BlueTape.PaymentService.Application.Models.Events.PaymentRequest;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Messages;
using BlueTape.ServiceBusMessaging.Attributes;
using MediatR;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.Application.Handlers.Events.Transaction;

public class TransactionProcessingEventHandler(
    IPaymentTransactionRepository paymentTransactionRepository,
    IPaymentTransactionHistoryRepository paymentTransactionHistoryRepository,
    INotificationMessageSender notificationMessageSender,
    ILogger<TransactionProcessingEventHandler> logger) : BaseTransactionEventHandler(paymentTransactionRepository, paymentTransactionHistoryRepository, logger),
    IRequestHandler<TransactionProcessingEvent>
{
    protected override IEnumerable<TransactionStatus> AllowedTransactionStatus => new[]
    {
        TransactionStatus.Processing
    };

    public Task Handle(TransactionProcessingEvent request, CancellationToken cancellationToken)
        => base.Handle(request, cancellationToken);

    protected sealed override async Task HandleAch(PaymentTransactionEntity transaction, CancellationToken ct)
    {
        if (transaction.PaymentRequest is null)
        {
            logger.LogWarning($"Unable to handle recalled transaction notifications for transactionId: {transaction.Id} due to payment request is null, paymentRequestId: {transaction.PaymentRequestId}");
            return;
        }

        switch (transaction.PaymentRequest.FlowTemplateCode)
        {
            case DomainConstants.DrawRepaymentCard:
            case DomainConstants.DrawRepayment:
            case DomainConstants.InvoicePaymentCard:
            case DomainConstants.InvoicePayment:
            case DomainConstants.InvoicePaymentV2:
            case DomainConstants.InvoiceDisbursementV2:
                break;
            case DomainConstants.DrawDisbursement:
            case DomainConstants.FinalPayment:
            case DomainConstants.FinalPaymentV2:
            case DomainConstants.FactoringDisbursement:
            case DomainConstants.FactoringFinalPayment:
                await HandleNotification(transaction, ct);
                break;
            default:
                logger.LogWarning($"Unable to handle payment request of type: {transaction.PaymentRequest.FlowTemplateCode}, paymentRequestId: {transaction.PaymentRequestId}");
                break;
        }
    }

    private async Task HandleNotification(PaymentTransactionEntity transaction, CancellationToken ct)
    {
        var transactionHistory = await GetTransactionHistory(transaction.Id, ct);

        var messagesToSend = new ServiceBusMessageBt<NotificationMessagePayloadV2>(new NotificationMessagePayloadV2
        {
            Id = transactionHistory!.Id,
            NotificationType = NotificationType.TransactionHistoryUpdate
        });

        await notificationMessageSender.SendMessage(messagesToSend, ct);
    }
}
