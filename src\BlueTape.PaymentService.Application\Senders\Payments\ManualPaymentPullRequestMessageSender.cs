﻿using BlueTape.PaymentService.Application.Abstractions.Senders.Payments;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.ManualPaymentPull;
using BlueTape.ServiceBusMessaging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.Application.Senders.Payments;

public class ManualPaymentPullMessageSender(
    IConfiguration configuration,
    ILogger<ManualPaymentPullRequestMessage> logger)
    : ServiceBusMessageSender<ManualPaymentPullRequestMessage>(configuration, logger,
            InfrastructureConstants.PaymentRequestQueueName, InfrastructureConstants.PaymentRequestQueueConnection),
        IManualPaymentPullMessageSender;
